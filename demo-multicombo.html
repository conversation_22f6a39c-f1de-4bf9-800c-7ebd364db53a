<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Webix 直接下拉多选示例</title>
  <link rel="stylesheet" href="./webix/webix.css" type="text/css">
  <script src="./webix/webix.js"></script>
  <script src="./webix/i18n/zh.js"></script>
</head>
<body>
  <div id="datatable_multicombo" style="width:800px; height:400px; margin:20px;"></div>

  <script>
    document.addEventListener("DOMContentLoaded", function() {
      webix.i18n.setLocale("zh-CN");

      // 模拟字典数据
      var skillOptions = [
        { id: "java", value: "Java开发" },
        { id: "python", value: "Python开发" },
        { id: "javascript", value: "JavaScript开发" },
        { id: "react", value: "React框架" },
        { id: "vue", value: "Vue框架" },
        { id: "angular", value: "Angular框架" },
        { id: "nodejs", value: "Node.js" },
        { id: "database", value: "数据库设计" },
        { id: "devops", value: "运维部署" },
        { id: "testing", value: "软件测试" }
      ];

      var departmentOptions = [
        { id: "dev", value: "开发部" },
        { id: "test", value: "测试部" },
        { id: "product", value: "产品部" },
        { id: "design", value: "设计部" },
        { id: "hr", value: "人事部" },
        { id: "finance", value: "财务部" }
      ];

      webix.ui({
        container: "datatable_multicombo",
        view: "datatable",
        editable: true,
        columns: [
          { id: "id", header: "ID", width: 50 },
          { id: "name", header: "姓名", width: 120, editor: "text" },
          { 
            id: "skills",
            header: "技能",
            width: 300,
            editor: "multicombo",
            options: skillOptions,
            template: function(obj) {
              if (!obj.skills || obj.skills === "") return "";
              // 将逗号分隔的值转换为显示文本
              var skillIds = obj.skills.toString().split(",");
              var skillNames = [];
              skillIds.forEach(function(skillId) {
                var skill = skillOptions.find(function(opt) { return opt.id === skillId.trim(); });
                if (skill) {
                  skillNames.push(skill.value);
                }
              });
              return skillNames.join(", ");
            }
          },
          { 
            id: "departments",
            header: "部门",
            width: 200,
            editor: "multicombo",
            options: departmentOptions,
            template: function(obj) {
              if (!obj.departments || obj.departments === "") return "";
              // 将逗号分隔的值转换为显示文本
              var deptIds = obj.departments.toString().split(",");
              var deptNames = [];
              deptIds.forEach(function(deptId) {
                var dept = departmentOptions.find(function(opt) { return opt.id === deptId.trim(); });
                if (dept) {
                  deptNames.push(dept.value);
                }
              });
              return deptNames.join(", ");
            }
          }
        ],
        data: [
          { id: 1, name: "张三", skills: "java,database", departments: "dev" },
          { id: 2, name: "李四", skills: "python,nodejs,testing", departments: "dev,test" },
          { id: 3, name: "王五", skills: "", departments: "" }
        ]
      });

      // 添加一些测试按钮
      webix.ui({
        container: document.body,
        rows: [
          { height: 10 },
          {
            cols: [
              { width: 20 },
              {
                view: "button",
                value: "获取所有数据",
                width: 120,
                click: function() {
                  var data = $$("datatable_multicombo").serialize();
                  console.log("表格数据:", data);
                  webix.message("数据已输出到控制台");
                }
              },
              {
                view: "button",
                value: "添加新行",
                width: 120,
                click: function() {
                  var grid = $$("datatable_multicombo");
                  var newId = grid.count() + 1;
                  grid.add({
                    id: newId,
                    name: "新员工" + newId,
                    skills: "",
                    departments: ""
                  });
                }
              }
            ]
          }
        ]
      });
    });
  </script>
</body>
</html>
