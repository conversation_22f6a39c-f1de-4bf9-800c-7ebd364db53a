<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Webix 日期时间输入示例</title>
  <link rel="stylesheet" href="./webix/webix.css" type="text/css">
  <script src="./webix/webix.js"></script>
  <script src="./webix/i18n/zh.js"></script>
</head>
<body>
  <div id="datatable_datetime" style="width:600px; height:300px; margin:20px;"></div>
  
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      webix.i18n.setLocale("zh-CN");

      var dateFormatter = webix.Date.dateToStr("%Y-%m-%d %H:%i");
      var dateParser   = webix.Date.strToDate("%Y-%m-%d %H:%i");

      webix.ui({
        container: "datatable_datetime",
        view: "datatable",
        editable: true,
        columns: [
          { id: "id", header: "ID", width: 50 },
          { id: "datetime", header: "日期时间", width: 250, editor: "text", css: "editable_cell",
            template: function(obj){
              if(!obj.datetime) return "";
              return dateFormatter(obj.datetime);
            }
          }
        ],
        data: [
          { id: 1, datetime: new Date() },
          { id: 2, datetime: null }
        ],
        on:{
          onBeforeEditStart:function(id){
            var item = this.getItem(id.row);
            var grid = this;
            var curVal = item[id.column] ? new Date(item[id.column]) : new Date();

            webix.ui({
              view:"window", modal:true, id:"datetime_window",
              head:"选择日期时间", position:"center", width:300,
              body:{
                rows:[
                  {
                    view:"datepicker",
                    id:"date_picker",
                    timepicker:true,
                    format:"%Y-%m-%d %H:%i",
                    value:curVal
                  },
                  {
                    cols:[
                      {
                        view:"button",
                        value:"确定",
                        css:"webix_primary",
                        click:function(){
                          var value = $$("date_picker").getValue();
                          item[id.column] = value;
                          grid.refresh(id.row);
                          $$("datetime_window").close();
                        }
                      },
                      {
                        view:"button",
                        value:"取消",
                        click:function(){
                          $$("datetime_window").close();
                        }
                      }
                    ]
                  }
                ]
              }
            }).show();

            return false; // 阻止默认编辑
          }
        }
      });
    });
  </script>
</body>
</html>
