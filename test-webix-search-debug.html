<!DOCTYPE html>
<html>
<head>
    <title>Webix搜索工具调试测试</title>
    <script type="text/javascript" src="https://cdn.webix.com/edge/webix.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.webix.com/edge/webix.css">
    <style>
        body { margin: 20px; font-family: Arial, sans-serif; }
        .debug-panel { 
            background: #f5f5f5; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
            border-left: 4px solid #007acc;
        }
        .debug-title { 
            font-weight: bold; 
            color: #007acc; 
            margin-bottom: 10px;
        }
        pre { 
            background: white; 
            padding: 10px; 
            border-radius: 3px; 
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Webix搜索工具调试测试</h1>
    
    <div class="debug-panel">
        <div class="debug-title">测试说明</div>
        <p>这个页面用于测试修复后的 WebixSearchUtil，特别关注字段值获取和过滤器构建的问题。</p>
        <p>请打开浏览器控制台查看详细的调试日志。</p>
    </div>

    <div id="searchContainer"></div>
    <div id="resultContainer" style="margin-top: 20px;"></div>

    <script>
        // 模拟搜索配置
        const searchConfig = [
            {
                view: 'text',
                searchLevel: 1,
                labelWidth: 65,
                id: 'ZL_PLAN__ZLTITLE',
                label: '招录名称',
                placeholder: '请输入招录名称'
            },
            {
                view: 'combo',
                searchLevel: 1,
                options: [
                    { id: '-99', value: '全部' },
                    { id: '1', value: '进行中' },
                    { id: '2', value: '已结束' },
                    { id: '3', value: '未开始' }
                ],
                labelWidth: 65,
                id: 'ZL_PLAN__ZLSTATUS',
                label: '状态',
                placeholder: '请选择状态',
                value: '-99'
            },
            {
                view: 'combo',
                searchLevel: 1,
                options: [
                    { id: '2025', value: '2025年' },
                    { id: '2024', value: '2024年' },
                    { id: '2023', value: '2023年' }
                ],
                labelWidth: 65,
                id: 'ZL_PLAN__ZLYEAR',
                label: '招录年份',
                placeholder: '请选择年份',
                value: '2025'
            },
            {
                view: 'daterangepicker',
                ranges: {
                    '本周': [new Date(), new Date()],
                    '本月': [new Date(), new Date()],
                    '今天': [new Date(), new Date()],
                    '上月': [new Date(), new Date()]
                },
                searchLevel: 2,
                format: '%Y-%m-%d %H:%i',
                timepicker: true,
                inputWidth: 130,
                label: '开始日期',
                labelWidth: 65,
                id: 'ZL_PLAN__BMSTARTDATE',
                placeholder: '请输入开始日期'
            },
            {
                view: 'text',
                searchLevel: 2,
                labelWidth: 65,
                id: 'ZL_PLAN__ZLLSH',
                label: '编号',
                placeholder: '请输入编号'
            }
        ];

        // 搜索选项
        const searchOptions = {
            columnsPerRow: 2,
            showButtons: true,
            onSearch: function(filter) {
                console.log("🎯 [测试] 搜索回调被调用");
                console.log("🎯 [测试] 接收到的过滤器:", filter);
                
                // 显示结果
                const resultHtml = `
                    <div class="debug-panel">
                        <div class="debug-title">搜索结果</div>
                        <pre>${JSON.stringify(filter, null, 2)}</pre>
                    </div>
                `;
                document.getElementById('resultContainer').innerHTML = resultHtml;
            },
            onRefresh: function() {
                console.log("🔄 [测试] 刷新回调被调用");
                document.getElementById('resultContainer').innerHTML = '';
            },
            onReset: function() {
                console.log("🔄 [测试] 重置回调被调用");
                document.getElementById('resultContainer').innerHTML = '';
            },
            onMoreSearch: function(filter) {
                console.log("🔍 [测试] 更多搜索回调被调用");
                console.log("🔍 [测试] 接收到的过滤器:", filter);

                // 验证日期范围字段是否正确包含（新格式）
                const dateRangeValue = filter['ZL_PLAN__BMSTARTDATE'];
                const hasDateRange = !!dateRangeValue;
                const dateRangeStatus = hasDateRange ?
                    `✅ 日期范围字段已包含: ${dateRangeValue}` :
                    '❌ 日期范围字段缺失';

                // 解析日期范围字符串（演示后端如何处理）
                let parsedDateRange = null;
                if (dateRangeValue && typeof dateRangeValue === 'string') {
                    const match = dateRangeValue.match(/#1(.*)#2(.*)/);
                    if (match) {
                        parsedDateRange = {
                            start: match[1] || null,
                            end: match[2] || null
                        };
                    }
                }

                // 显示结果
                const resultHtml = `
                    <div class="debug-panel">
                        <div class="debug-title">高级搜索结果</div>
                        <p><strong>${dateRangeStatus}</strong></p>
                        ${parsedDateRange ? `
                        <div class="debug-panel" style="margin-top: 10px;">
                            <div class="debug-title">后端解析示例</div>
                            <pre>${JSON.stringify(parsedDateRange, null, 2)}</pre>
                        </div>
                        ` : ''}
                        <pre>${JSON.stringify(filter, null, 2)}</pre>
                    </div>
                `;
                document.getElementById('resultContainer').innerHTML = resultHtml;
            }
        };

        // 等待页面加载完成
        webix.ready(function() {
            console.log("🚀 [测试] 页面加载完成，开始初始化");
            
            // 加载修复后的 WebixSearchUtil
            fetch('doc/2.txt')
                .then(response => response.text())
                .then(code => {
                    console.log("📁 [测试] WebixSearchUtil 代码加载完成");
                    
                    // 执行代码
                    eval(code);
                    
                    console.log("🔧 [测试] 开始构建搜索UI");
                    
                    // 构建搜索UI
                    const searchUI = WebixSearchUtil.buildSearchUI(searchConfig, searchOptions);
                    
                    console.log("🎨 [测试] 搜索UI构建完成:", searchUI);
                    
                    // 创建界面
                    webix.ui({
                        container: "searchContainer",
                        rows: [
                            {
                                view: "toolbar",
                                elements: [
                                    { view: "label", label: "搜索测试界面" }
                                ]
                            },
                            {
                                view: "form",
                                elements: searchUI.defaultUI
                            }
                        ]
                    });
                    
                    console.log("✅ [测试] 界面创建完成");
                })
                .catch(error => {
                    console.error("❌ [测试] 加载失败:", error);
                    document.getElementById('searchContainer').innerHTML = 
                        '<div class="debug-panel"><div class="debug-title">错误</div><p>无法加载 WebixSearchUtil 代码: ' + error.message + '</p></div>';
                });
        });
    </script>
</body>
</html>
