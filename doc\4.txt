package com.yuehui.project.csras.zl_plan_project.service.position;

import com.yuehui.project.csras.common.QuerydslTableService;

import java.util.Map;

/**
 * 招录职位上报Service接口
 * 
 * <AUTHOR>
 */
public interface ZLPlanProjectPositionSubmitService extends QuerydslTableService {
    
    /**
     * 检查所有记录是否都已结束上报
     * 
     * @param zlId 招录计划ID
     * @return true-所有记录都已结束上报，false-还有未结束上报的记录
     */
    boolean checkAllISJIESHUStatus(String zlId);
    
    /**
     * 批量更新结束上报状态
     * 
     * @param zlId 招录计划ID
     * @return 更新的记录数
     */
    int batchUpdateISJIESHUStatus(String zlId);
    
    /**
     * 获取结束上报统计数据
     * 
     * @param zlId 招录计划ID
     * @return 统计数据Map，包含unfinishedCount（未结束上报数量）和finishedCount（已结束上报数量）
     */
    Map<String, Integer> getISJIESHUStatistics(String zlId);
    
    /**
     * 更新指定记录的字段
     * 
     * @param zlPositionSubmitId 记录ID
     * @param fieldUpdates 要更新的字段Map
     * @return 是否更新成功
     */
    boolean updateFields(String zlPositionSubmitId, Map<String, Object> fieldUpdates);
}
