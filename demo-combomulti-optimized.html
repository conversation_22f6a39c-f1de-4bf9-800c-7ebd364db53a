<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Webix 优化的下拉多选示例</title>
  <link rel="stylesheet" href="./webix/webix.css" type="text/css">
  <script src="./webix/webix.js"></script>
  <script src="./webix/i18n/zh.js"></script>
  <style>
    .multiselect-cell {
      cursor: pointer;
      position: relative;
    }
    .multiselect-cell:after {
      content: "▼";
      position: absolute;
      right: 5px;
      top: 50%;
      transform: translateY(-50%);
      color: #999;
      font-size: 12px;
    }
    .multiselect-cell:hover {
      background-color: #f5f5f5;
    }
  </style>
</head>
<body>
  <div id="datatable_multiselect" style="width:800px; height:400px; margin:20px;"></div>

  <script>
    document.addEventListener("DOMContentLoaded", function() {
      webix.i18n.setLocale("zh-CN");

      // 模拟字典数据
      var skillOptions = [
        { id: "java", value: "Java开发" },
        { id: "python", value: "Python开发" },
        { id: "javascript", value: "JavaScript开发" },
        { id: "react", value: "React框架" },
        { id: "vue", value: "Vue框架" },
        { id: "angular", value: "Angular框架" },
        { id: "nodejs", value: "Node.js" },
        { id: "database", value: "数据库设计" },
        { id: "devops", value: "运维部署" },
        { id: "testing", value: "软件测试" }
      ];

      var departmentOptions = [
        { id: "dev", value: "开发部" },
        { id: "test", value: "测试部" },
        { id: "product", value: "产品部" },
        { id: "design", value: "设计部" },
        { id: "hr", value: "人事部" },
        { id: "finance", value: "财务部" }
      ];

      // 全局变量存储当前选择
      var currentSelection = [];

      webix.ui({
        container: "datatable_multiselect",
        view: "datatable",
        editable: true,
        columns: [
          { id: "id", header: "ID", width: 50 },
          { id: "name", header: "姓名", width: 120, editor: "text" },
          { 
            id: "skills",
            header: "技能",
            width: 300,
            editor: "text", // 使用text编辑器，但会被onBeforeEditStart拦截
            css: "multiselect-cell",
            template: function(obj) {
              if (!obj.skills || obj.skills === "") return '<span style="color:#999;">点击选择技能</span>';
              // 将逗号分隔的值转换为显示文本
              var skillIds = obj.skills.split(",");
              var skillNames = [];
              skillIds.forEach(function(skillId) {
                var skill = skillOptions.find(function(opt) { return opt.id === skillId.trim(); });
                if (skill) {
                  skillNames.push(skill.value);
                }
              });
              return skillNames.join(", ");
            }
          },
          { 
            id: "departments",
            header: "部门",
            width: 200,
            editor: "text", // 使用text编辑器，但会被onBeforeEditStart拦截
            css: "multiselect-cell",
            template: function(obj) {
              if (!obj.departments || obj.departments === "") return '<span style="color:#999;">点击选择部门</span>';
              // 将逗号分隔的值转换为显示文本
              var deptIds = obj.departments.split(",");
              var deptNames = [];
              deptIds.forEach(function(deptId) {
                var dept = departmentOptions.find(function(opt) { return opt.id === deptId.trim(); });
                if (dept) {
                  deptNames.push(dept.value);
                }
              });
              return deptNames.join(", ");
            }
          }
        ],
        data: [
          { id: 1, name: "张三", skills: "java,database", departments: "dev" },
          { id: 2, name: "李四", skills: "python,nodejs,testing", departments: "dev,test" },
          { id: 3, name: "王五", skills: "", departments: "" }
        ],
        on: {
          onBeforeEditStart: function(id) {
            var item = this.getItem(id.row);
            var grid = this;
            var fieldName = id.column;

            // 只处理多选字段
            if (fieldName !== "skills" && fieldName !== "departments") {
              return true; // 允许默认编辑
            }

            var options, windowTitle;
            if (fieldName === "skills") {
              options = skillOptions;
              windowTitle = "选择技能";
            } else if (fieldName === "departments") {
              options = departmentOptions;
              windowTitle = "选择部门";
            }

            // 获取当前值并转换为数组
            var currentValue = item[fieldName] || "";
            currentSelection = currentValue ? currentValue.split(",").map(function(v) { return v.trim(); }) : [];

            webix.ui({
              view: "window",
              modal: true,
              id: "multiselect_window",
              head: windowTitle,
              position: "center",
              width: 350,
              height: 400,
              body: {
                rows: [
                  {
                    view: "list",
                    id: "multiselect_list",
                    select: "multiselect",
                    template: function(obj) {
                      var isSelected = currentSelection.indexOf(obj.id) !== -1;
                      var checkIcon = isSelected ? "☑" : "☐";
                      return '<div style="padding: 5px;">' +
                             '<span style="margin-right: 8px; font-size: 14px;">' + checkIcon + '</span>' +
                             '<span>' + obj.value + '</span>' +
                             '</div>';
                    },
                    data: options,
                    height: 280,
                    scroll: "y",
                    on: {
                      onItemClick: function(id) {
                        var optionId = this.getItem(id).id;
                        var index = currentSelection.indexOf(optionId);
                        if (index === -1) {
                          currentSelection.push(optionId);
                        } else {
                          currentSelection.splice(index, 1);
                        }
                        this.refresh();
                        return false; // 阻止默认选择行为
                      }
                    }
                  },
                  {
                    cols: [
                      {
                        view: "button",
                        value: "全选",
                        width: 60,
                        click: function() {
                          currentSelection = options.map(function(opt) { return opt.id; });
                          $$("multiselect_list").refresh();
                        }
                      },
                      {
                        view: "button",
                        value: "清空",
                        width: 60,
                        click: function() {
                          currentSelection = [];
                          $$("multiselect_list").refresh();
                        }
                      },
                      {},
                      {
                        view: "button",
                        value: "确定",
                        css: "webix_primary",
                        width: 60,
                        click: function() {
                          item[fieldName] = currentSelection.join(",");
                          grid.refresh(id.row);
                          $$("multiselect_window").close();
                        }
                      },
                      {
                        view: "button",
                        value: "取消",
                        width: 60,
                        click: function() {
                          $$("multiselect_window").close();
                        }
                      }
                    ]
                  }
                ]
              }
            }).show();

            return false; // 阻止默认编辑
          }
        }
      });

      // 添加一些测试按钮
      webix.ui({
        container: document.body,
        rows: [
          { height: 10 },
          {
            cols: [
              { width: 20 },
              {
                view: "button",
                value: "获取所有数据",
                width: 120,
                click: function() {
                  var data = $$("datatable_multiselect").serialize();
                  console.log("表格数据:", data);
                  webix.message("数据已输出到控制台");
                }
              },
              {
                view: "button",
                value: "添加新行",
                width: 120,
                click: function() {
                  var grid = $$("datatable_multiselect");
                  var newId = grid.count() + 1;
                  grid.add({
                    id: newId,
                    name: "新员工" + newId,
                    skills: "",
                    departments: ""
                  });
                }
              },
              {
                view: "button",
                value: "设置技能",
                width: 120,
                click: function() {
                  var grid = $$("datatable_multiselect");
                  if (grid.count() > 0) {
                    var item = grid.getItem(grid.getFirstId());
                    item.skills = "java,react,database";
                    grid.refresh();
                  }
                }
              }
            ]
          }
        ]
      });
    });
  </script>
</body>
</html>
