package com.yuehui.project.csras.zl_plan_project.service.a_impl.position;

import com.yuehui.common.utils.security.ShiroUtils;
import com.yuehui.project.csras.common.QuerydslTableServiceImpl;
import com.yuehui.project.csras.zl_plan_project.service.position.ZLPlanProjectPositionSubmitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
public class ZLPlanProjectPositionSubmitServiceImpl extends QuerydslTableServiceImpl implements ZLPlanProjectPositionSubmitService {
    @Override
    protected String getTableId() {
        return "ZL_PLAN_POSITION_SUBMIT";
    }

    @Override
    protected String getPkColumn() {
        return "ZL_POSITION_SUBMIT_ID";
    }

    @Override
    protected String getTableViewId() {
        return "view_zl_position_submit";
    }
    @Override
    protected String getJoinCondition() {
        return "ZL_PLAN_POSITION_SUBMIT.ZLID = ZL_PLAN.ZLID AND " +
                "ZL_PLAN_POSITION_SUBMIT.ZLUID = SYS_DEPT.DEPT_ID";
    }

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public ZLPlanProjectPositionSubmitServiceImpl(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }


    @Override
    public boolean checkAllISJIESHUStatus(String zlId) {
        String sql = "SELECT COUNT(*) FROM ZL_PLAN_POSITION_SUBMIT WHERE ZLID = ? AND (ISJIESHU IS NULL OR ISJIESHU != 1)";

        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, zlId);

        // 如果count为0，说明所有记录的ISJIESHU都是1
        return count != null && count == 0;
    }

    @Override
    @Transactional
    public int batchUpdateISJIESHUStatus(String zlId) {
        // 修改SQL，同时处理ISJIESHU为null和0的情况
        String sql = "UPDATE ZL_PLAN_POSITION_SUBMIT SET ISJIESHU = 1 WHERE ZLID = ? AND (ISJIESHU IS NULL OR ISJIESHU = 0)";

        int updateCount = jdbcTemplate.update(sql, zlId);

        log.info("批量更新ISJIESHU状态，ZLID: {}, 更新记录数: {}", zlId, updateCount);

        return updateCount;
    }

    @Override
    public Map<String, Integer> getISJIESHUStatistics(String zlId) {
        // 查询未结束上报的数量（ISJIESHU为null或0）
        String unfinishedSql = "SELECT COUNT(*) FROM ZL_PLAN_POSITION_SUBMIT WHERE ZLID = ? AND (ISJIESHU IS NULL OR ISJIESHU = 0)";
        Integer unfinishedCount = jdbcTemplate.queryForObject(unfinishedSql, Integer.class, zlId);

        // 查询已结束上报的数量（ISJIESHU为1）
        String finishedSql = "SELECT COUNT(*) FROM ZL_PLAN_POSITION_SUBMIT WHERE ZLID = ? AND ISJIESHU = 1";
        Integer finishedCount = jdbcTemplate.queryForObject(finishedSql, Integer.class, zlId);

        Map<String, Integer> statistics = new HashMap<>();
        statistics.put("unfinishedCount", unfinishedCount != null ? unfinishedCount : 0);
        statistics.put("finishedCount", finishedCount != null ? finishedCount : 0);

        log.info("获取结束上报统计数据，ZLID: {}, 未结束: {}, 已结束: {}",
                zlId, statistics.get("unfinishedCount"), statistics.get("finishedCount"));

        return statistics;
    }

    @Override
    @Transactional
    public boolean updateFields(String zlPositionSubmitId, Map<String, Object> fieldUpdates) {
        try {
            if (fieldUpdates == null || fieldUpdates.isEmpty()) {
                return false;
            }

            // 验证所有字段名
            Map<String, Object> validFields = new HashMap<>();
            for (Map.Entry<String, Object> entry : fieldUpdates.entrySet()) {
                String fieldName = entry.getKey();
                Object fieldValue = entry.getValue();

                String validFieldName = validateFieldName(fieldName);
                if (validFieldName != null) {
                    validFields.put(validFieldName, fieldValue);
                } else {
                    log.warn("跳过无效字段: {}", fieldName);
                }
            }

            if (validFields.isEmpty()) {
                log.warn("没有有效的字段需要更新");
                return false;
            }

            // 构建动态SQL
            StringBuilder sql = new StringBuilder("UPDATE ZL_PLAN_POSITION_SUBMIT SET ");
            List<Object> params = new ArrayList<>();

            boolean first = true;
            for (Map.Entry<String, Object> entry : validFields.entrySet()) {
                if (!first) {
                    sql.append(", ");
                }
                sql.append(entry.getKey()).append(" = ?");
                params.add(entry.getValue());
                first = false;
            }

            sql.append(" WHERE ZL_POSITION_SUBMIT_ID = ?");
            params.add(zlPositionSubmitId);

            int updateCount = jdbcTemplate.update(sql.toString(), params.toArray());

            log.info("批量更新字段: ID={}, 字段数={}, 影响行数={}, 字段={}",
                    zlPositionSubmitId, validFields.size(), updateCount, validFields.keySet());

            return updateCount > 0;
        } catch (Exception e) {
            log.error("批量更新字段失败: ID={}, 字段={}",
                    zlPositionSubmitId, fieldUpdates, e);
            throw e;
        }
    }

    /**
     * 验证字段名，防止SQL注入
     * @param fieldName 字段名
     * @return 验证后的字段名，如果无效返回null
     */
    private String validateFieldName(String fieldName) {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return null;
        }
        // 去掉前缀，只保留最后一个"__"后的部分
        int idx = fieldName.lastIndexOf("__");
        String dbField = (idx >= 0) ? fieldName.substring(idx + 2) : fieldName;

        // 直接在这里写白名单判断
        if ("ISJIESHU".equals(dbField) || "ZLPMSTATUS".equals(dbField)) {
            return dbField;
        }

        log.warn("尝试更新不允许的字段: {}", dbField);
        return null;
    }

}