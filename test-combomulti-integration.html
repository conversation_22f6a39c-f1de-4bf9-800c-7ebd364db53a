<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>测试 ComboMulti 集成</title>
  <link rel="stylesheet" href="./webix/webix.css" type="text/css">
  <script src="./webix/webix.js"></script>
  <script src="./webix/i18n/zh.js"></script>
</head>
<body>
  <div id="search_container" style="margin: 20px;"></div>
  <div id="result_container" style="margin: 20px;"></div>

  <script>
    // 引入 WebixSearchUtil（这里模拟引入，实际应该从文件加载）
    // 这里只包含测试需要的核心方法
    const WebixSearchUtil = {
        CONSTANTS: {
            VIEWS: {
                COMBOMULTI: "combomulti"
            }
        },
        
        _initStyles: function() {
            if (document.getElementById('webix-search-util-styles')) {
                return;
            }
            
            const style = document.createElement('style');
            style.id = 'webix-search-util-styles';
            style.textContent = `
                .multiselect-field {
                    cursor: pointer !important;
                    position: relative;
                }
                .multiselect-field:after {
                    content: "▼";
                    position: absolute;
                    right: 8px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #999;
                    font-size: 12px;
                    pointer-events: none;
                }
                .multiselect-field:hover {
                    background-color: #f5f5f5 !important;
                }
                .multiselect-field input {
                    cursor: pointer !important;
                }
            `;
            document.head.appendChild(style);
        },

        _createComboMultiField: function(field, prefix) {
            const self = this;
            
            return {
                view: "text",
                id: prefix + field.id,
                label: field.label,
                placeholder: field.placeholder || "点击选择" + (field.label || ""),
                width: field.inputWidth || 200,
                readonly: true,
                css: "multiselect-field",
                on: {
                    onItemClick: function() {
                        self._showMultiSelectWindow(field, prefix);
                    },
                    onChange: function(newValue) {
                        self._updateMultiSelectDisplay(this, field, newValue);
                    }
                }
            };
        },

        _updateMultiSelectDisplay: function(component, field, value) {
            if (!component || !component.getInputNode) {
                return;
            }
            
            const inputNode = component.getInputNode();
            if (!inputNode) {
                return;
            }
            
            if (!value || value === "") {
                inputNode.value = field.placeholder || "点击选择" + (field.label || "");
                inputNode.style.color = "#999";
                return;
            }
            
            const selectedIds = value.split(",");
            const selectedNames = [];
            
            if (field.options && Array.isArray(field.options)) {
                selectedIds.forEach(function(id) {
                    const option = field.options.find(function(opt) { 
                        return opt.id === id.trim() || opt.value === id.trim(); 
                    });
                    if (option) {
                        selectedNames.push(option.value || option.text || option.label);
                    }
                });
            }
            
            inputNode.value = selectedNames.join(", ");
            inputNode.style.color = "#333";
        },

        _showMultiSelectWindow: function(field, prefix) {
            const fieldComponent = $$(prefix + field.id);
            
            if (!fieldComponent || !field.options) {
                console.warn("多选字段组件或选项未找到:", field.id);
                return;
            }

            const currentValue = fieldComponent.getValue() || "";
            let currentArray = currentValue ? currentValue.split(",").map(function(v) { return v.trim(); }) : [];

            webix.ui({
                view: "window",
                modal: true,
                id: "multiselect_window_" + field.id,
                head: "选择" + (field.label || "选项"),
                position: "center",
                width: 400,
                height: 350,
                body: {
                    rows: [
                        {
                            view: "list",
                            id: "multiselect_list_" + field.id,
                            template: function(obj) {
                                const optionId = obj.id || obj.value;
                                const checked = currentArray.indexOf(optionId) !== -1 ? "checked" : "";
                                return '<div style="padding: 5px; cursor: pointer;">' +
                                       '<input type="checkbox" ' + checked + ' onchange="window.toggleMultiSelection_' + field.id + '(\'' + optionId + '\')" style="margin-right: 8px;">' +
                                       '<span>' + (obj.value || obj.text || obj.label) + '</span>' +
                                       '</div>';
                            },
                            data: field.options,
                            height: 220,
                            scroll: "y"
                        },
                        {
                            cols: [
                                {
                                    view: "button",
                                    value: "全选",
                                    width: 80,
                                    click: function() {
                                        currentArray = field.options.map(function(opt) { 
                                            return opt.id || opt.value; 
                                        });
                                        $$("multiselect_list_" + field.id).refresh();
                                    }
                                },
                                {
                                    view: "button",
                                    value: "清空",
                                    width: 80,
                                    click: function() {
                                        currentArray = [];
                                        $$("multiselect_list_" + field.id).refresh();
                                    }
                                },
                                {},
                                {
                                    view: "button",
                                    value: "确定",
                                    css: "webix_primary",
                                    width: 80,
                                    click: function() {
                                        const newValue = currentArray.join(",");
                                        fieldComponent.setValue(newValue);
                                        $$("multiselect_window_" + field.id).close();
                                    }
                                },
                                {
                                    view: "button",
                                    value: "取消",
                                    width: 80,
                                    click: function() {
                                        $$("multiselect_window_" + field.id).close();
                                    }
                                }
                            ]
                        }
                    ]
                }
            }).show();

            window["toggleMultiSelection_" + field.id] = function(optionId) {
                const index = currentArray.indexOf(optionId);
                if (index === -1) {
                    currentArray.push(optionId);
                } else {
                    currentArray.splice(index, 1);
                }
            };
        }
    };

    document.addEventListener("DOMContentLoaded", function() {
        webix.i18n.setLocale("zh-CN");
        
        // 初始化样式
        WebixSearchUtil._initStyles();

        // 模拟搜索字段配置
        const skillField = {
            id: "skills",
            label: "技能",
            view: "combomulti",
            placeholder: "请选择技能",
            inputWidth: 300,
            options: [
                { id: "java", value: "Java开发" },
                { id: "python", value: "Python开发" },
                { id: "javascript", value: "JavaScript开发" },
                { id: "react", value: "React框架" },
                { id: "vue", value: "Vue框架" }
            ]
        };

        const deptField = {
            id: "departments",
            label: "部门",
            view: "combomulti",
            placeholder: "请选择部门",
            inputWidth: 200,
            options: [
                { id: "dev", value: "开发部" },
                { id: "test", value: "测试部" },
                { id: "product", value: "产品部" },
                { id: "design", value: "设计部" }
            ]
        };

        // 创建搜索表单
        const searchForm = {
            view: "form",
            id: "search_form",
            elements: [
                {
                    cols: [
                        WebixSearchUtil._createComboMultiField(skillField, "default_"),
                        { width: 20 },
                        WebixSearchUtil._createComboMultiField(deptField, "default_")
                    ]
                },
                {
                    cols: [
                        {
                            view: "button",
                            value: "获取值",
                            width: 100,
                            click: function() {
                                const skills = $$("default_skills").getValue();
                                const departments = $$("default_departments").getValue();
                                
                                $$("result_display").setHTML(
                                    "<h3>当前选择的值：</h3>" +
                                    "<p><strong>技能：</strong>" + (skills || "无") + "</p>" +
                                    "<p><strong>部门：</strong>" + (departments || "无") + "</p>"
                                );
                            }
                        },
                        {
                            view: "button",
                            value: "设置值",
                            width: 100,
                            click: function() {
                                $$("default_skills").setValue("java,react");
                                $$("default_departments").setValue("dev,test");
                            }
                        },
                        {
                            view: "button",
                            value: "清空",
                            width: 100,
                            click: function() {
                                $$("default_skills").setValue("");
                                $$("default_departments").setValue("");
                            }
                        }
                    ]
                }
            ]
        };

        webix.ui({
            container: "search_container",
            rows: [
                { view: "label", label: "ComboMulti 集成测试", css: "webix_header" },
                searchForm
            ]
        });

        webix.ui({
            container: "result_container",
            view: "template",
            id: "result_display",
            template: "<h3>点击'获取值'按钮查看当前选择</h3>",
            height: 150
        });
    });
  </script>
</body>
</html>
