<!DOCTYPE html>
<html>
<head>
    <title>适配测试 - datetimerangepicker & combomulti</title>
    <script type="text/javascript" src="https://cdn.webix.com/edge/webix.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.webix.com/edge/webix.css">
    <style>
        body { margin: 20px; font-family: Arial, sans-serif; }
        .test-panel { 
            background: #f0f8ff; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
            border-left: 4px solid #2196F3;
        }
        .success-panel { 
            background: #f0fff0; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .error-panel { 
            background: #fff5f5; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
            border-left: 4px solid #f44336;
        }
        pre { 
            background: white; 
            padding: 10px; 
            border-radius: 3px; 
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>适配测试：datetimerangepicker & combomulti</h1>
    
    <div class="test-panel">
        <h3>测试说明</h3>
        <p>这个测试验证新增的视图类型是否正确适配：</p>
        <ul>
            <li><strong>datetimerangepicker</strong> - 日期时间范围选择器</li>
            <li><strong>combomulti</strong> - 多选下拉框</li>
        </ul>
        <p>请打开浏览器控制台查看详细日志。</p>
    </div>

    <div id="searchContainer"></div>
    <div id="resultContainer"></div>

    <script>
        // 测试配置，包含新的视图类型
        const searchConfig = [
            {
                view: 'combo',
                searchLevel: 1,
                options: [
                    { id: '2025', value: '2025年' },
                    { id: '2024', value: '2024年' }
                ],
                labelWidth: 80,
                id: 'YEAR_FIELD',
                label: '年份',
                value: '2025'
            },
            {
                view: 'combomulti',
                searchLevel: 1,
                options: [
                    { id: 'option1', value: '选项1' },
                    { id: 'option2', value: '选项2' },
                    { id: 'option3', value: '选项3' },
                    { id: 'option4', value: '选项4' }
                ],
                labelWidth: 80,
                id: 'MULTI_FIELD',
                label: '多选字段',
                separator: ',',
                placeholder: '请选择多个选项'
            },
            {
                view: 'daterangepicker',
                searchLevel: 2,
                format: '%Y-%m-%d',
                timepicker: false,
                inputWidth: 300,
                label: '日期范围',
                labelWidth: 80,
                id: 'DATE_FIELD',
                placeholder: '请选择日期范围'
            },
            {
                view: 'datetimerangepicker',
                searchLevel: 2,
                format: '%Y-%m-%d %H:%i',
                timepicker: true,
                inputWidth: 300,
                label: '日期时间范围',
                labelWidth: 80,
                id: 'DATETIME_FIELD',
                placeholder: '请选择日期时间范围'
            }
        ];

        const searchOptions = {
            columnsPerRow: 1,
            showButtons: true,
            onMoreSearch: function(filter) {
                console.log("🎯 [测试] 更多搜索回调，接收到过滤器:", filter);
                
                let resultHtml = '';
                let hasErrors = false;
                
                // 检查多选下拉
                const multiValue = filter['MULTI_FIELD_IN'];
                if (multiValue) {
                    resultHtml += `
                        <div class="success-panel">
                            <h4>✅ 多选下拉测试成功</h4>
                            <p><strong>字段：</strong> MULTI_FIELD_IN</p>
                            <p><strong>值：</strong> ${JSON.stringify(multiValue)}</p>
                        </div>
                    `;
                } else {
                    resultHtml += `
                        <div class="error-panel">
                            <h4>❌ 多选下拉测试失败</h4>
                            <p>未找到 MULTI_FIELD_IN 字段</p>
                        </div>
                    `;
                    hasErrors = true;
                }
                
                // 检查日期时间范围
                const datetimeValue = filter['DATETIME_FIELD'];
                if (datetimeValue && datetimeValue.includes('#1') && datetimeValue.includes('#2')) {
                    resultHtml += `
                        <div class="success-panel">
                            <h4>✅ 日期时间范围测试成功</h4>
                            <p><strong>字段：</strong> DATETIME_FIELD</p>
                            <p><strong>值：</strong> ${datetimeValue}</p>
                        </div>
                    `;
                } else {
                    resultHtml += `
                        <div class="error-panel">
                            <h4>❌ 日期时间范围测试失败</h4>
                            <p>未找到正确的 DATETIME_FIELD 值</p>
                        </div>
                    `;
                    hasErrors = true;
                }
                
                // 显示完整过滤器
                resultHtml += `
                    <div class="test-panel">
                        <h4>完整过滤器结果</h4>
                        <pre>${JSON.stringify(filter, null, 2)}</pre>
                    </div>
                `;
                
                // 总结
                if (!hasErrors) {
                    resultHtml = `
                        <div class="success-panel">
                            <h3>🎉 所有测试通过！</h3>
                            <p>datetimerangepicker 和 combomulti 适配成功</p>
                        </div>
                    ` + resultHtml;
                }
                
                document.getElementById('resultContainer').innerHTML = resultHtml;
            }
        };

        // 页面加载完成后初始化
        webix.ready(function() {
            console.log("🚀 开始加载 WebixSearchUtil...");
            
            fetch('doc/2.txt')
                .then(response => response.text())
                .then(code => {
                    console.log("📁 WebixSearchUtil 代码加载完成");
                    eval(code);
                    
                    console.log("🔧 构建搜索UI...");
                    const searchUI = WebixSearchUtil.buildSearchUI(searchConfig, searchOptions);
                    
                    webix.ui({
                        container: "searchContainer",
                        rows: [
                            {
                                view: "toolbar",
                                elements: [
                                    { view: "label", label: "适配测试界面" }
                                ]
                            },
                            {
                                view: "form",
                                elements: searchUI.defaultUI
                            }
                        ]
                    });
                    
                    console.log("✅ 界面创建完成，请选择多选字段和日期时间范围，然后点击'更多查询'按钮测试");
                })
                .catch(error => {
                    console.error("❌ 加载失败:", error);
                    document.getElementById('resultContainer').innerHTML = 
                        `<div class="error-panel"><h3>加载错误</h3><p>${error.message}</p></div>`;
                });
        });
    </script>
</body>
</html>
