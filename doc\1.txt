<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>招录事项管理</title>
    <th:block th:include="include :: header('招录事项管理')" />
    <style>
        html, body { height: 100%; margin: 0; padding: 0; background: #f5f6fa; }
        .myhover {
            background: #e6f7ff !important;
        }
        /* 更高优先级 */
        .custom-border .webix_cell,
        .custom-border .webix_hcell {
            border-right: 0.5px solid #ddd !important;
            border-bottom: 0.5px solid #ddd !important;
        }
    </style>
</head>
<body>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var ctx = [[@{/}]];
    var ZL_PLAN__ZLID = /*[[${ZL_PLAN__ZLID}]]*/ '';
    var isAdminOrZZB = /*[[${isAdminOrZZB}]]*/ {};
    var isHost = /*[[${isHost}]]*/ {};
    var isEmploy = /*[[${isEmploy}]]*/ {};
    var ISALLJSCB = /*[[${ISALLJSCB}]]*/ '';

</script>
<script type="text/javascript" th:src="@{/js/webix-utils/webix-search-util.js(t=${#dates.createNow().getTime()})}"></script>
<script type="text/javascript" th:src="@{/js/webix-utils/webix-table-util.js(t=${#dates.createNow().getTime()})}"></script>

<script>
    var lastFilter = {};

    webix.ready(function(){
        Promise.all([
            webix.ajax().get(ctx + "zl-plan-project/position-submit/searchConfig?ZL_PLAN__ZLID=" + encodeURIComponent(ZL_PLAN__ZLID)),
            webix.ajax().get(ctx + "zl-plan-project/position-submit/columns?ZL_PLAN__ZLID=" + encodeURIComponent(ZL_PLAN__ZLID))
        ]).then(function([searchConfigRes, columnsRes]){
            var searchFields = searchConfigRes.json();
            var columns = columnsRes.json();
            columns = WebixTableUtil.buildWebixColumns(columns);

            // 使用 WebixSearchUtil 构建搜索UI
            var searchUI = WebixSearchUtil.buildSearchUI(searchFields, {
                columnsPerRow: 2,  // 高级搜索每行显示2列
                showButtons: true,
                onSearch: function() {
                    doSearch(false);
                },
                onRefresh: function() {
                    refreshGrid();
                },
                onMoreSearch: function(filter) {
                    lastFilter = { ...filter, ZL_PLAN__ZLID: ZL_PLAN__ZLID };
                    var grid = $$("zlPlanTableGrid");
                    grid.clearAll();
                    grid.load(grid.config.url);
                }
            });

            // 分页配置
            var pager = {
                view: "pager",
                id: "zlPlanPager",
                size: 20,
                group: 5,
                template: "{common.first()} {common.prev()} {common.pages()} {common.next()} {common.last()} <div style='float: right;'>共#limit#页，每页 #size# 条，共 #count# 条</div>",
                sizes: [10, 20, 30, 50]
            };

            // 表格配置
            var grid = {
                view: "datatable",
                hover: "myhover",
                id: "zlPlanTableGrid",
                css: "zlplan-table-autoheight custom-border",
                columns: [
                    // 序号列
                    {
                        id: "index",
                        header: "序号",
                        width: 55,
                        css: { "text-align": "center" },
                        template: function(obj) {
                            var grid = $$("zlPlanTableGrid");
                            if (!grid) return "";
                            var page = 0, size = 20;
                            if (grid.getState) {
                                var state = grid.getState() || {};
                                page = Number(state.page) || 0;
                                size = Number(state.size) || 20;
                            }
                            var index = grid.getIndexById(obj.id);
                            if (typeof index === "number" && !isNaN(index)) {
                                return (page * size) + index + 1;
                            } else {
                                return "";
                            }
                        }
                    },
                    // 你的其它列
                    ...columns
                ],
                select: "row",
                datafetch: 20,
                pager: "zlPlanPager",
                url: function(params) {
                    params = params || {start: 0, count: 20};
                    var filter = Object.assign({}, lastFilter, { ZL_PLAN__ZLID: ZL_PLAN__ZLID });
                    return webix.ajax()
                        .headers({
                            "Content-Type": "application/json",
                            "Accept": "application/json"
                        })
                        .post(
                            ctx + "zl-plan-project/position-submit/page?pos=" + params.start + "&count=" + params.count,
                            JSON.stringify(filter)
                        );
                },
                header: true,
                borders: true,
                gridlines: true,
                separateRows: true,
                tooltip: true,
                resizeColumn: true,
                datatype: "json",
                editable: true,
                editaction: "click",    // 指定单击进入编辑模式
                navigation: true,       // 添加导航支持
                on: {
                    onBeforeLoad: function() {
                        this.showOverlay("加载中...");
                    },
                    onAfterLoad: function() {
                        this.hideOverlay();
                        if (!this.count()) this.showOverlay("无数据");
                    },
                    // 双击跳转（对于不可编辑的列）
                    onItemDblClick: function(id, e, node) {
                        var item = this.getItem(id);
                        console.log("--",item)
                        if (item && item.ZL_PLAN_POSITION_SUBMIT__ZLUID) {
                            window.location.href = ctx + "zl-plan-project/position?ZL_PLAN__ZLID=" + encodeURIComponent(ZL_PLAN__ZLID) + "&ZL_PLAN_POSITION_SUBMIT__ZLUID=" + encodeURIComponent(item.ZL_PLAN_POSITION_SUBMIT__ZLUID);
                        }
                    },
                    // 编辑开始前
                    onBeforeEditStart: function(cell) {
                        console.log("准备开始编辑:", cell);
                        return true;  // 返回true允许编辑
                    },
                    // 编辑开始后
                    onAfterEditStart: function(cell) {
                        console.log("开始编辑:", cell);
                    },
                    // 编辑结束前
                    onBeforeEditStop: function(state, editor) {
                        console.log("准备结束编辑:", state);
                        return true;
                    },
                    // 编辑结束后 - 这里处理保存逻辑
                    onAfterEditStop: function(state, editor) {
                        if (!state || state.value === state.old) {
                            console.log("值未变化，不保存");
                            return;
                        }
                        var selectedId = this.getSelectedId();
                        var item = null;
                        if (selectedId) {
                            item = this.getItem(selectedId);
                            console.log("获取的行数据:", item);
                        }
                        if (!item || !item.ZL_PLAN_POSITION_SUBMIT__ZL_POSITION_SUBMIT_ID) {
                            console.log("缺少必要的数据，不保存");
                            return;
                        }
                        // 关键：用 editor.column 获取列ID
                        var columnId = (editor && editor.column) || state.column;
                        if (!columnId) {
                            console.log("无法获取列ID，不保存");
                            return;
                        }
                        var fieldUpdates = {};
                        fieldUpdates[columnId] = state.value;

                        console.log("===", fieldUpdates)
                        webix.ajax().headers({
                            "Content-Type": "application/json"
                        }).post(
                            ctx + "zl-plan-project/position-submit/saveRowData",
                            JSON.stringify({
                                ZL_PLAN_POSITION_SUBMIT__ZL_POSITION_SUBMIT_ID: item.ZL_PLAN_POSITION_SUBMIT__ZL_POSITION_SUBMIT_ID,
                                fieldUpdates: fieldUpdates
                            })
                        ).then(function(response) {
                            var res = response.json();
                            console.log("保存响应:", res);
                            if (res.code === 0) {
                                layer.msg( "保存成功", {icon: 1});
                                refreshGridAndBtn();
                            } else {
                                layer.msg( "保存失败", {icon: 2});
                                this.setItem(state.row, columnId, state.old);
                            }
                        }.bind(this)).fail(function(err) {
                            layer.msg("保存失败：" + (err.responseText || "未知错误"), {icon: 2});
                            this.setItem(state.row, columnId, state.old);
                        }.bind(this));
                    }
                }
            };

            // 定义顶部工具栏
            var toolbar = {
                view: "toolbar",
                padding: 10,
                cols: [
                    {},  // 左侧空白，使按钮靠右
                    {
                        view: "button",
                        id: "ISJIESHUBtn",
                        width: 100,
                        value: (ISALLJSCB) ? "已结束上报" : "结束上报",
                        css: (ISALLJSCB) ? "custom_btn_green" : "webix_primary",
                        hidden: !(isAdminOrZZB || isHost || isEmploy), // 没权限直接不显示
                        disabled: !isAdminOrZZB, // 只要是isEmploy就禁用
                        click: function () {
                            if (!ISALLJSCB) {
                                showISJIESHUConfirm();
                            }
                        }
                    },
                    {
                        view: "button",
                        type: "form",
                        value: "导出简章",
                        width: 90,
                        css: "webix_primary",
                        icon: "wxi-upload",
                        click: function () {
                            // 直接调用后端接口获取报表URL
                            webix.ajax().get(ctx + "/zl-plan-project/position-submit/report", {
                                reportId: "3d57db978db24ca9b1d165b5b4aa247c.rpt",
                                zlid:ZL_PLAN__ZLID
                            }).then(function(response) {
                                var data = response.json();
                                window.open(data.url, '_blank');
                            }).fail(function(err) {
                                layer.msg("获取报表链接失败", {icon: 2});
                            });
                        }
                    },
                    {
                        view: "button",
                        id: "publishBtn",
                        type: "form",
                        value: "发布",
                        width: 90,
                        disabled: true,
                        css: "webix_primary",
                        icon: "wxi-download",
                        click: function() {
                            // 1. 获取当前 zlid
                            var zlid = ZL_PLAN__ZLID || ""; // 你实际的变量名
                            if (!zlid) {
                                layer.msg("缺少招录事项ID！", {icon: 2});
                                return;
                            }
                            // 2. 调用后端接口
                            webix.ajax().headers({
                                "Content-Type": "application/json"
                            }).post("/csras/syncData/pushPosition?zlid=" + encodeURIComponent(zlid))
                                .then(function(response) {
                                    var res = response.json();
                                    if (res.code === 0) {
                                        webix.message({type: "success", text: res.msg || "发布成功！"});
                                        // 发布成功后刷新数据和按钮状态
                                        setTimeout(function() {
                                            refreshGridAndBtn();
                                        }, 300);
                                    } else {
                                        layer.msg(res.msg || "发布失败！", {icon: 2});
                                    }
                                })
                                .fail(function(err) {
                                    layer.msg("发布失败：" + (err.responseText || "未知错误"), {icon: 2});
                                });
                        }
                    },
                ]
            };

            // 构建主布局
            webix.ui({
                view: "layout",
                id: "mainLayout",
                rows: [
                    toolbar,  // 添加工具栏
                    {
                        view: "form",
                        id: "searchForm",
                        css: "search-bar",
                        padding: 10,
                        cols: searchUI.defaultUI
                    },
                    {
                        rows: [grid, pager]
                    }
                ]
            });

            function doSearch(includeAdvanced) {
                var filter = WebixSearchUtil.buildFilter(searchFields, {
                    ...getDefaultValues(),
                    ...(includeAdvanced ? getAdvancedValues() : {})
                });
                lastFilter = { ...filter, ZL_PLAN__ZLID: ZL_PLAN__ZLID };
                var grid = $$("zlPlanTableGrid");
                grid.clearAll();
                grid.load(grid.config.url);
            }

            function getDefaultValues() {
                var values = {};
                searchFields.forEach(function(field) {
                    if (field.searchLevel === WebixSearchUtil.CONSTANTS.SEARCH_LEVEL.DEFAULT) {
                        values[field.id] = WebixSearchUtil.getFieldValue(field.id);
                    }
                });
                return values;
            }

            function getAdvancedValues() {
                var values = {};
                searchFields.forEach(function(field) {
                    values[field.id] = WebixSearchUtil.getFieldValue(field.id, "advanced_");
                });
                return values;
            }

            function refreshGrid() {
                WebixSearchUtil.resetFields(searchFields, function(fieldId, value) {
                    WebixSearchUtil.setFieldValue(fieldId, value);
                });
                lastFilter = { ZL_PLAN__ZLID: ZL_PLAN__ZLID };
                var grid = $$("zlPlanTableGrid");
                grid.clearAll();
                grid.load(grid.config.url);
            }

            function showISJIESHUConfirm() {
                // 从后台获取真实的统计数据，而不是依赖当前页面的记录
                webix.ajax().post(ctx + "zl-plan-project/position-submit/getISJIESHUStatistics", {
                    ZL_PLAN_POSITION_SUBMIT__ZLID: ZL_PLAN__ZLID
                }).then(function (response) {
                    var res = response.json();
                    if (res.code === 0) {
                        var statistics = res.data;
                        var count0 = statistics.unfinishedCount || 0; // 未结束上报
                        var count1 = statistics.finishedCount || 0;   // 已结束上报
                        var unfinishedCount = count0;

                        let msg = "未结束上报: 【" + count0 + "】<br>"
                            + "已结束上报: 【" + count1 + "】<br>";

                        // 如果有未结束上报的记录，显示提示并直接调用后端更新
                        if (unfinishedCount > 0) {
                            webix.confirm({
                                title: "结束上报确认",
                                text: "当前有【" + unfinishedCount + "】条记录未结束上报，是否要结束上报？<br>" + msg,
                                ok: "结束上报",
                                cancel: "取消",
                                callback: function (result) {
                                    if (result) {
                                        // 简化：只传递ZLID，后端直接批量更新
                                        webix.ajax().post(ctx + "zl-plan-project/position-submit/batchUpdateISJIESHUStatus", {
                                            ZL_PLAN_POSITION_SUBMIT__ZLID: ZL_PLAN__ZLID
                                        }).then(function (response) {
                                            var res = response.json();
                                            if (res.code === 0) {
                                                layer.msg(res.msg, {icon: 1});
                                                // 延迟刷新，确保后端数据更新完成
                                                setTimeout(function() {
                                                    refreshGridAndBtn();
                                                }, 500);
                                            } else {
                                                layer.msg(res.msg, {icon: 0});
                                            }
                                        }).fail(function (err) {
                                            layer.msg("更新失败：" + (err.responseText || "未知错误"), {icon: 2});
                                        });
                                    }
                                }
                            });
                        } else {
                            webix.alert({
                                type: "alert-info",
                                title: "提示",
                                text: "所有记录都已结束上报。<br>" + msg,
                                ok: "确定"
                            });
                        }
                    } else {
                        layer.msg("获取统计数据失败：" + res.msg, {icon: 2});
                    }
                }).fail(function (err) {
                    layer.msg("获取统计数据失败：" + (err.responseText || "未知错误"), {icon: 2});
                });
            }


            function refreshGridAndBtn() {
                refreshGrid();
                setTimeout(checkISJIESHUBtnStatus, 300); // 等表格数据加载完再检测按钮
            }

            function checkISJIESHUBtnStatus() {
                var grid = $$("zlPlanTableGrid");
                var allData = [];
                grid.data.each(function (obj) {
                    allData.push(obj);
                });
                // ISJIESHU 可能是数字或字符串
                var allFinished = allData.length > 0 && allData.every(item => item.ZL_PLAN_POSITION_SUBMIT__ISJIESHU == 1);
                var btn = $$("ISJIESHUBtn");
                if (btn) {
                    if (allFinished) {
                        btn.define("css", "custom_btn_green");
                        btn.setValue("已结束上报");
                        btn.disable();
                    } else {
                        btn.define("css", "webix_primary");
                        btn.setValue("结束上报");
                        btn.enable();
                    }
                    btn.refresh();
                }

                // 控制发布按钮
                var publishBtn = $$("publishBtn");
                if (publishBtn) {
                    if (allFinished) {
                        publishBtn.enable();
                    } else {
                        publishBtn.disable();
                    }
                    publishBtn.refresh();
                }
            }
        });
    });
</script>
</body>
</html>
