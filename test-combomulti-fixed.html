<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>测试 ComboMulti 修复版集成</title>
  <link rel="stylesheet" href="./webix/webix.css" type="text/css">
  <script src="./webix/webix.js"></script>
  <script src="./webix/i18n/zh.js"></script>
</head>
<body>
  <div id="search_container" style="margin: 20px;"></div>
  <div id="result_container" style="margin: 20px;"></div>

  <script>
    // 模拟从2.txt加载的WebixSearchUtil（简化版，只包含combomulti相关功能）
    const WebixSearchUtil = {
        CONSTANTS: {
            VIEWS: {
                COMBOMULTI: "combomulti"
            }
        },
        
        _initMultiSelectStyles: function() {
            if (document.getElementById('webix-multiselect-styles')) {
                return;
            }
            
            const style = document.createElement('style');
            style.id = 'webix-multiselect-styles';
            style.textContent = `
                .webix_multiselect_field {
                    cursor: pointer !important;
                    position: relative;
                }
                .webix_multiselect_field:after {
                    content: "▼";
                    position: absolute;
                    right: 8px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #999;
                    font-size: 12px;
                    pointer-events: none;
                }
                .webix_multiselect_field:hover {
                    background-color: #f5f5f5 !important;
                }
                .webix_multiselect_field input {
                    cursor: pointer !important;
                }
            `;
            document.head.appendChild(style);
        },

        _createComboMultiField: function(field, prefix) {
            const self = this;
            
            return {
                view: "text",
                id: prefix + field.id,
                placeholder: field.placeholder || "点击选择" + (field.label || ""),
                width: field.inputWidth || 200,
                readonly: true,
                css: "webix_multiselect_field",
                on: {
                    onItemClick: function() {
                        self._showMultiSelectWindow(field, prefix);
                    },
                    onChange: function(newValue) {
                        self._updateMultiSelectDisplay(this, field, newValue);
                    }
                }
            };
        },

        _updateMultiSelectDisplay: function(component, field, value) {
            if (!component || !component.getInputNode) {
                return;
            }
            
            const inputNode = component.getInputNode();
            if (!inputNode) {
                return;
            }
            
            if (!value || value === "") {
                inputNode.value = field.placeholder || "点击选择" + (field.label || "");
                inputNode.style.color = "#999";
                return;
            }
            
            // 将逗号分隔的代码值转换为显示文本
            const selectedCodes = value.split(",");
            const selectedNames = [];
            
            if (field.options && Array.isArray(field.options)) {
                selectedCodes.forEach(function(code) {
                    const option = field.options.find(function(opt) { 
                        return opt.id === code.trim(); 
                    });
                    if (option) {
                        selectedNames.push(option.value);
                    }
                });
            }
            
            inputNode.value = selectedNames.join(", ");
            inputNode.style.color = "#333";
        },

        _showMultiSelectWindow: function(field, prefix) {
            const fieldComponent = $$(prefix + field.id);
            
            if (!fieldComponent || !field.options) {
                console.warn("多选字段组件或选项未找到:", field.id);
                return;
            }

            // 获取当前值并转换为数组（存储的是代码值）
            const currentValue = fieldComponent.getValue() || "";
            let currentArray = currentValue ? currentValue.split(",").map(function(v) { return v.trim(); }) : [];

            // 创建多选窗口
            webix.ui({
                view: "window",
                modal: true,
                id: "multiselect_window_" + field.id,
                head: "选择" + (field.label || "选项"),
                position: "center",
                width: 400,
                height: 350,
                body: {
                    rows: [
                        {
                            view: "list",
                            id: "multiselect_list_" + field.id,
                            template: function(obj) {
                                const checked = currentArray.indexOf(obj.id) !== -1 ? "checked" : "";
                                return '<div style="padding: 5px; cursor: pointer;">' +
                                       '<input type="checkbox" ' + checked + ' onchange="window.toggleMultiSelection_' + field.id + '(\'' + obj.id + '\')" style="margin-right: 8px;">' +
                                       '<span>' + obj.value + '</span>' +
                                       '</div>';
                            },
                            data: field.options,
                            height: 220,
                            scroll: "y"
                        },
                        {
                            cols: [
                                {
                                    view: "button",
                                    value: "全选",
                                    width: 80,
                                    click: function() {
                                        currentArray = field.options.map(function(opt) { 
                                            return opt.id; 
                                        });
                                        $$("multiselect_list_" + field.id).refresh();
                                    }
                                },
                                {
                                    view: "button",
                                    value: "清空",
                                    width: 80,
                                    click: function() {
                                        currentArray = [];
                                        $$("multiselect_list_" + field.id).refresh();
                                    }
                                },
                                {},
                                {
                                    view: "button",
                                    value: "确定",
                                    css: "webix_primary",
                                    width: 80,
                                    click: function() {
                                        // 保存的是代码值，不是显示名称
                                        const newValue = currentArray.join(",");
                                        fieldComponent.setValue(newValue);
                                        $$("multiselect_window_" + field.id).close();
                                    }
                                },
                                {
                                    view: "button",
                                    value: "取消",
                                    width: 80,
                                    click: function() {
                                        $$("multiselect_window_" + field.id).close();
                                    }
                                }
                            ]
                        }
                    ]
                }
            }).show();

            // 定义全局函数用于切换选择状态
            window["toggleMultiSelection_" + field.id] = function(optionId) {
                const index = currentArray.indexOf(optionId);
                if (index === -1) {
                    currentArray.push(optionId);
                } else {
                    currentArray.splice(index, 1);
                }
            };
        }
    };

    document.addEventListener("DOMContentLoaded", function() {
        webix.i18n.setLocale("zh-CN");
        
        // 初始化样式
        WebixSearchUtil._initMultiSelectStyles();

        // 模拟搜索字段配置（注意：存储的是代码值，显示的是名称）
        const statusField = {
            id: "status",
            label: "状态",
            view: "combomulti",
            placeholder: "请选择状态",
            inputWidth: 200,
            options: [
                { id: "01", value: "待审核" },
                { id: "02", value: "审核中" },
                { id: "03", value: "已通过" },
                { id: "04", value: "已拒绝" },
                { id: "05", value: "已撤销" }
            ]
        };

        const deptField = {
            id: "dept",
            label: "部门",
            view: "combomulti",
            placeholder: "请选择部门",
            inputWidth: 200,
            options: [
                { id: "D001", value: "开发部" },
                { id: "D002", value: "测试部" },
                { id: "D003", value: "产品部" },
                { id: "D004", value: "设计部" }
            ]
        };

        // 创建搜索表单
        const searchForm = {
            view: "form",
            id: "search_form",
            elements: [
                {
                    cols: [
                        { view: "label", label: "状态:", width: 60 },
                        WebixSearchUtil._createComboMultiField(statusField, "default_"),
                        { width: 20 },
                        { view: "label", label: "部门:", width: 60 },
                        WebixSearchUtil._createComboMultiField(deptField, "default_")
                    ]
                },
                {
                    cols: [
                        {
                            view: "button",
                            value: "获取值",
                            width: 100,
                            click: function() {
                                const status = $$("default_status").getValue();
                                const dept = $$("default_dept").getValue();
                                
                                $$("result_display").setHTML(
                                    "<h3>当前选择的值（代码值）：</h3>" +
                                    "<p><strong>状态：</strong>" + (status || "无") + "</p>" +
                                    "<p><strong>部门：</strong>" + (dept || "无") + "</p>" +
                                    "<p style='color: #666; font-size: 12px;'>注意：传递给后端的是代码值（如01,02），显示的是名称（如待审核,审核中）</p>"
                                );
                            }
                        },
                        {
                            view: "button",
                            value: "设置值",
                            width: 100,
                            click: function() {
                                // 设置代码值
                                $$("default_status").setValue("01,03");
                                $$("default_dept").setValue("D001,D002");
                            }
                        },
                        {
                            view: "button",
                            value: "清空",
                            width: 100,
                            click: function() {
                                $$("default_status").setValue("");
                                $$("default_dept").setValue("");
                            }
                        }
                    ]
                }
            ]
        };

        webix.ui({
            container: "search_container",
            rows: [
                searchForm
            ]
        });

        webix.ui({
            container: "result_container",
            view: "template",
            id: "result_display",
            template: "<h3>点击'获取值'按钮查看当前选择</h3>",
            height: 150
        });
    });
  </script>
</body>
</html>
