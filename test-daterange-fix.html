<!DOCTYPE html>
<html>
<head>
    <title>日期范围修复测试</title>
    <script type="text/javascript" src="https://cdn.webix.com/edge/webix.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.webix.com/edge/webix.css">
    <style>
        body { margin: 20px; font-family: Arial, sans-serif; }
        .result-panel { 
            background: #f0f8ff; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .error-panel { 
            background: #fff5f5; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
            border-left: 4px solid #f44336;
        }
        pre { 
            background: white; 
            padding: 10px; 
            border-radius: 3px; 
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>日期范围字段修复测试</h1>
    
    <div class="result-panel">
        <h3>测试说明</h3>
        <p>这个测试验证日期范围字段现在返回拼接字符串格式：<code>#1开始日期#2结束日期</code></p>
        <p>请打开浏览器控制台查看详细日志，然后点击"更多查询"按钮测试。</p>
    </div>

    <div id="searchContainer"></div>
    <div id="resultContainer"></div>

    <script>
        // 简化的搜索配置，只包含关键字段
        const searchConfig = [
            {
                view: 'combo',
                searchLevel: 1,
                options: [
                    { id: '2025', value: '2025年' },
                    { id: '2024', value: '2024年' }
                ],
                labelWidth: 65,
                id: 'ZL_PLAN__ZLYEAR',
                label: '招录年份',
                value: '2025'
            },
            {
                view: 'daterangepicker',
                searchLevel: 2,
                format: '%Y-%m-%d %H:%i',
                timepicker: true,
                inputWidth: 300,
                label: '开始日期',
                labelWidth: 65,
                id: 'ZL_PLAN__BMSTARTDATE',
                placeholder: '请选择日期范围'
            }
        ];

        const searchOptions = {
            columnsPerRow: 1,
            showButtons: true,
            onMoreSearch: function(filter) {
                console.log("🎯 [测试] 更多搜索回调，接收到过滤器:", filter);
                
                const dateRangeValue = filter['ZL_PLAN__BMSTARTDATE'];
                let resultHtml = '';
                
                if (dateRangeValue) {
                    // 解析日期范围字符串
                    const match = dateRangeValue.match(/#1(.*)#2(.*)/);
                    const parsed = match ? {
                        start: match[1] || '未设置',
                        end: match[2] || '未设置'
                    } : null;
                    
                    resultHtml = `
                        <div class="result-panel">
                            <h3>✅ 测试成功！</h3>
                            <p><strong>日期范围字段值：</strong> <code>${dateRangeValue}</code></p>
                            <h4>后端解析结果：</h4>
                            <pre>${JSON.stringify(parsed, null, 2)}</pre>
                            <h4>完整过滤器：</h4>
                            <pre>${JSON.stringify(filter, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultHtml = `
                        <div class="error-panel">
                            <h3>❌ 测试失败</h3>
                            <p>日期范围字段值缺失</p>
                            <h4>接收到的过滤器：</h4>
                            <pre>${JSON.stringify(filter, null, 2)}</pre>
                        </div>
                    `;
                }
                
                document.getElementById('resultContainer').innerHTML = resultHtml;
            }
        };

        // 页面加载完成后初始化
        webix.ready(function() {
            console.log("🚀 开始加载 WebixSearchUtil...");
            
            fetch('doc/2.txt')
                .then(response => response.text())
                .then(code => {
                    console.log("📁 WebixSearchUtil 代码加载完成");
                    eval(code);
                    
                    console.log("🔧 构建搜索UI...");
                    const searchUI = WebixSearchUtil.buildSearchUI(searchConfig, searchOptions);
                    
                    webix.ui({
                        container: "searchContainer",
                        rows: [
                            {
                                view: "toolbar",
                                elements: [
                                    { view: "label", label: "日期范围修复测试" }
                                ]
                            },
                            {
                                view: "form",
                                elements: [
                                    { cols: searchUI.defaultUI }
                                ]
                            }
                        ]
                    });
                    
                    console.log("✅ 界面创建完成，请点击'更多查询'按钮测试");
                })
                .catch(error => {
                    console.error("❌ 加载失败:", error);
                    document.getElementById('resultContainer').innerHTML = 
                        `<div class="error-panel"><h3>加载错误</h3><p>${error.message}</p></div>`;
                });
        });
    </script>
</body>
</html>
