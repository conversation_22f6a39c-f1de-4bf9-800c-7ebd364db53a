<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>ComboMulti 调试测试 - 简化版</title>
  <link rel="stylesheet" href="./webix/webix.css" type="text/css">
  <script src="./webix/webix.js"></script>
  <script src="./webix/i18n/zh.js"></script>
</head>
<body>
  <div id="search_container" style="margin: 20px;"></div>
  <div id="debug_container" style="margin: 20px;"></div>

  <script>
    // 简化的WebixSearchUtil，只包含ComboMulti相关功能
    const WebixSearchUtil = {
        CONSTANTS: {
            VIEWS: {
                COMBOMULTI: "combomulti"
            }
        },
        
        buildFilter: function(searchConfig, values) {
            console.log("🔍 [buildFilter] 开始构建过滤器");
            console.log("🔍 [buildFilter] 搜索配置:", searchConfig);
            console.log("🔍 [buildFilter] 输入值:", values);
            
            const filter = {};
            
            searchConfig.forEach((field) => {
                const value = values[field.id];
                console.log(`🔍 [buildFilter] 处理字段: ${field.id} = ${value} (${field.view})`);
                
                if (field.view === this.CONSTANTS.VIEWS.COMBOMULTI) {
                    // 处理多选下拉
                    console.log(`🔍 [ComboMulti] 处理多选下拉字段: ${field.id}`);
                    console.log(`🔍 [ComboMulti] 原始值:`, value, `类型: ${typeof value}`);
                    
                    if (value) {
                        if (Array.isArray(value)) {
                            // 如果是数组，直接使用
                            if (value.length > 0) {
                                filter[field.id] = value;
                                console.log(`🔍 [ComboMulti] 数组值处理: ${field.id} = [${value.join(', ')}]`);
                            }
                        } else if (typeof value === "string" && value.trim() !== "") {
                            // 如果是逗号分隔的字符串，转换为数组
                            const valueArray = value.split(",").map(v => v.trim()).filter(v => v !== "");
                            if (valueArray.length > 0) {
                                filter[field.id] = valueArray;
                                console.log(`🔍 [ComboMulti] 字符串值处理: ${field.id} = [${valueArray.join(', ')}]`);
                                console.log(`🔍 [ComboMulti] 字段选项:`, field.options);
                            }
                        }
                    } else {
                        console.log(`🔍 [ComboMulti] 字段 ${field.id} 值为空，跳过`);
                    }
                } else {
                    // 其他字段类型
                    if (value && value.toString().trim() !== "") {
                        filter[field.id] = value;
                    }
                }
            });
            
            console.log("🔍 [buildFilter] 最终过滤器:", filter);
            return filter;
        },
        
        _initMultiSelectStyles: function() {
            if (document.getElementById('webix-multiselect-styles')) {
                return;
            }
            
            const style = document.createElement('style');
            style.id = 'webix-multiselect-styles';
            style.textContent = `
                .webix_multiselect_field {
                    cursor: pointer !important;
                    position: relative;
                }
                .webix_multiselect_field:after {
                    content: "▼";
                    position: absolute;
                    right: 8px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #999;
                    font-size: 12px;
                    pointer-events: none;
                }
                .webix_multiselect_field:hover {
                    background-color: #f5f5f5 !important;
                }
                .webix_multiselect_field input {
                    cursor: pointer !important;
                }
            `;
            document.head.appendChild(style);
        },

        _createComboMultiField: function(field, prefix) {
            console.log(`🔍 [ComboMulti] 创建多选字段: ${field.id}`);
            console.log(`🔍 [ComboMulti] 字段配置:`, field);
            
            const self = this;
            
            return {
                view: "text",
                id: prefix + field.id,
                placeholder: field.placeholder || "点击选择" + (field.label || ""),
                width: field.inputWidth || 200,
                readonly: true,
                css: "webix_multiselect_field",
                on: {
                    onItemClick: function() {
                        self._showMultiSelectWindow(field, prefix);
                    },
                    onChange: function(newValue) {
                        self._updateMultiSelectDisplay(this, field, newValue);
                    }
                }
            };
        },

        _updateMultiSelectDisplay: function(component, field, value) {
            if (!component || !component.getInputNode) {
                return;
            }
            
            const inputNode = component.getInputNode();
            if (!inputNode) {
                return;
            }
            
            if (!value || value === "") {
                inputNode.value = field.placeholder || "点击选择" + (field.label || "");
                inputNode.style.color = "#999";
                return;
            }
            
            // 将逗号分隔的代码值转换为显示文本
            const selectedCodes = value.split(",");
            const selectedNames = [];
            
            if (field.options && Array.isArray(field.options)) {
                selectedCodes.forEach(function(code) {
                    const option = field.options.find(function(opt) { 
                        return opt.id === code.trim(); 
                    });
                    if (option) {
                        selectedNames.push(option.value);
                    }
                });
            }
            
            inputNode.value = selectedNames.join(", ");
            inputNode.style.color = "#333";
        },

        _showMultiSelectWindow: function(field, prefix) {
            console.log(`🔍 [ComboMulti] 显示多选窗口: ${field.id}`);
            
            const fieldComponent = $$(prefix + field.id);
            
            if (!fieldComponent || !field.options) {
                console.error(`🔍 [ComboMulti] 组件或选项未找到: ${field.id}`, {
                    component: !!fieldComponent,
                    options: !!field.options,
                    optionsLength: field.options ? field.options.length : 0
                });
                return;
            }

            // 获取当前值并转换为数组（存储的是代码值）
            const currentValue = fieldComponent.getValue() || "";
            let currentArray = currentValue ? currentValue.split(",").map(function(v) { return v.trim(); }) : [];
            
            console.log(`🔍 [ComboMulti] 当前值: "${currentValue}"`);
            console.log(`🔍 [ComboMulti] 当前数组:`, currentArray);
            console.log(`🔍 [ComboMulti] 字段选项:`, field.options);

            // 创建多选窗口
            webix.ui({
                view: "window",
                modal: true,
                id: "multiselect_window_" + field.id,
                head: "选择" + (field.label || "选项"),
                position: "center",
                width: 400,
                height: 350,
                body: {
                    rows: [
                        {
                            view: "list",
                            id: "multiselect_list_" + field.id,
                            template: function(obj) {
                                const checked = currentArray.indexOf(obj.id) !== -1 ? "checked" : "";
                                return '<div style="padding: 5px; cursor: pointer;">' +
                                       '<input type="checkbox" ' + checked + ' onchange="window.toggleMultiSelection_' + field.id + '(\'' + obj.id + '\')" style="margin-right: 8px;">' +
                                       '<span>' + obj.value + '</span>' +
                                       '</div>';
                            },
                            data: field.options,
                            height: 220,
                            scroll: "y"
                        },
                        {
                            cols: [
                                {
                                    view: "button",
                                    value: "全选",
                                    width: 80,
                                    click: function() {
                                        currentArray = field.options.map(function(opt) { 
                                            return opt.id; 
                                        });
                                        $$("multiselect_list_" + field.id).refresh();
                                    }
                                },
                                {
                                    view: "button",
                                    value: "清空",
                                    width: 80,
                                    click: function() {
                                        currentArray = [];
                                        $$("multiselect_list_" + field.id).refresh();
                                    }
                                },
                                {},
                                {
                                    view: "button",
                                    value: "确定",
                                    css: "webix_primary",
                                    width: 80,
                                    click: function() {
                                        // 保存的是代码值，不是显示名称
                                        const newValue = currentArray.join(",");
                                        console.log(`🔍 [ComboMulti] 确定选择: ${field.id}`);
                                        console.log(`🔍 [ComboMulti] 选择的代码值:`, currentArray);
                                        console.log(`🔍 [ComboMulti] 拼接后的值: "${newValue}"`);
                                        fieldComponent.setValue(newValue);
                                        $$("multiselect_window_" + field.id).close();
                                    }
                                },
                                {
                                    view: "button",
                                    value: "取消",
                                    width: 80,
                                    click: function() {
                                        $$("multiselect_window_" + field.id).close();
                                    }
                                }
                            ]
                        }
                    ]
                }
            }).show();

            // 定义全局函数用于切换选择状态
            window["toggleMultiSelection_" + field.id] = function(optionId) {
                console.log(`🔍 [ComboMulti] 切换选项: ${optionId}`);
                const index = currentArray.indexOf(optionId);
                if (index === -1) {
                    currentArray.push(optionId);
                    console.log(`🔍 [ComboMulti] 添加选项，当前数组:`, currentArray);
                } else {
                    currentArray.splice(index, 1);
                    console.log(`🔍 [ComboMulti] 移除选项，当前数组:`, currentArray);
                }
            };
        }
    };

    document.addEventListener("DOMContentLoaded", function() {
      webix.i18n.setLocale("zh-CN");
      
      // 初始化样式
      WebixSearchUtil._initMultiSelectStyles();

      // 模拟搜索配置
      const searchConfig = [
        {
          id: "ZL_PLAN__ZLSTATUS",
          label: "状态",
          view: "combomulti",
          placeholder: "请选择状态",
          inputWidth: 200,
          options: [
            { id: "01", value: "起草" },
            { id: "02", value: "流转" },
            { id: "03", value: "核准" },
            { id: "04", value: "驳回" },
            { id: "05", value: "撤销" }
          ]
        },
        {
          id: "ZL_PLAN__ZLYEAR",
          label: "年份",
          view: "text",
          placeholder: "请输入年份",
          inputWidth: 120
        }
      ];

      // 创建搜索表单
      webix.ui({
        container: "search_container",
        rows: [
          { view: "label", label: "ComboMulti 调试测试 - 请打开浏览器控制台查看详细日志", css: "webix_header" },
          {
            view: "form",
            id: "search_form",
            elements: [
              {
                cols: [
                  { view: "label", label: "状态:", width: 60 },
                  WebixSearchUtil._createComboMultiField(searchConfig[0], "default_"),
                  { width: 20 },
                  { view: "label", label: "年份:", width: 60 },
                  {
                    view: "text",
                    id: "default_ZL_PLAN__ZLYEAR",
                    placeholder: "请输入年份",
                    width: 120
                  }
                ]
              },
              {
                cols: [
                  {
                    view: "button",
                    value: "搜索",
                    width: 80,
                    click: function() {
                      console.log("🔍 [测试] 开始搜索");
                      
                      // 获取表单值
                      const values = {
                        "ZL_PLAN__ZLSTATUS": $$("default_ZL_PLAN__ZLSTATUS").getValue(),
                        "ZL_PLAN__ZLYEAR": $$("default_ZL_PLAN__ZLYEAR").getValue()
                      };
                      
                      console.log("🔍 [测试] 表单值:", values);
                      
                      // 构建过滤器
                      const filter = WebixSearchUtil.buildFilter(searchConfig, values);
                      
                      // 显示结果
                      $$("debug_display").setHTML(
                        "<h3>搜索结果：</h3>" +
                        "<pre>" + JSON.stringify(filter, null, 2) + "</pre>" +
                        "<h4>检查点：</h4>" +
                        "<ul>" +
                        "<li>字段名是否包含_IN？" + (Object.keys(filter).some(k => k.includes('_IN')) ? "<span style='color:red'>是</span>" : "<span style='color:green'>否</span>") + "</li>" +
                        "<li>状态值类型：" + (filter["ZL_PLAN__ZLSTATUS"] ? (Array.isArray(filter["ZL_PLAN__ZLSTATUS"]) ? "数组" : typeof filter["ZL_PLAN__ZLSTATUS"]) : "无值") + "</li>" +
                        "<li>状态值内容：" + (filter["ZL_PLAN__ZLSTATUS"] ? JSON.stringify(filter["ZL_PLAN__ZLSTATUS"]) : "无值") + "</li>" +
                        "</ul>"
                      );
                    }
                  },
                  {
                    view: "button",
                    value: "设置测试值",
                    width: 120,
                    click: function() {
                      $$("default_ZL_PLAN__ZLSTATUS").setValue("01,02,03");
                      $$("default_ZL_PLAN__ZLYEAR").setValue("2025");
                    }
                  },
                  {
                    view: "button",
                    value: "清空",
                    width: 80,
                    click: function() {
                      $$("default_ZL_PLAN__ZLSTATUS").setValue("");
                      $$("default_ZL_PLAN__ZLYEAR").setValue("");
                    }
                  }
                ]
              }
            ]
          }
        ]
      });

      webix.ui({
        container: "debug_container",
        view: "template",
        id: "debug_display",
        template: "<h3>调试信息将显示在这里</h3><p>请：</p><ol><li>打开浏览器控制台</li><li>点击状态字段进行多选</li><li>点击搜索按钮</li><li>查看控制台中的 [ComboMulti] 调试信息</li></ol>",
        height: 300
      });

      console.log("🔍 [测试] 页面加载完成，请进行测试");
    });
  </script>
</body>
</html>
