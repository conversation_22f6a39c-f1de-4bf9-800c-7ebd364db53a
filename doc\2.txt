package com.yuehui.project.csras.csras_sync;

import com.yuehui.project.csras.common.BatchLoader;
import com.yuehui.project.csras.common.BatchLoaderResultSet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 招录简章同步器：招录平台内网 --> 中间库
 * <AUTHOR> 2025.07.21
 */
@Component @Lazy
public class SyncZlPosition {
    private static final Logger log = LoggerFactory.getLogger(SyncZlPosition.class);

    private final JdbcTemplate localJdbcTemplate;
    private final JdbcTemplate remoteJdbcTemplate;

    // 招录简章相关表
    private final Map<String, String> tables;

    // 记录装载器
    private BatchLoader batchLoader;

    public SyncZlPosition(
            @Autowired JdbcTemplate localJdbcTemplate,
            @Qualifier("zjkDataSource") DataSource zjkDataSource
    ) {
        super();
        this.localJdbcTemplate = localJdbcTemplate;
        this.remoteJdbcTemplate = new JdbcTemplate(zjkDataSource);
        this.tables = initSyncTables();
    }

    private Map<String, String> initSyncTables() {
        Map<String, String> map = new LinkedHashMap<>();

        // 用人单位简章表
        map.put("ZL_PLAN_POSITION", "ZLPCONTAINER_ZLPOSITION");
        // 招录单位上报表
        map.put("ZL_PLAN_POSITION_SUBMIT", "ZLPCONTAINER");

        return map;
    }

    /**
     * 内网简章 推送到 中间库
     * @param zlid 招录事项ID
     */
    public String doPush(String zlid) {
        long startTime = System.currentTimeMillis();

        // 1. 检查状态是否允许同步
        String statusCheckResult = checkSyncStatus(zlid);
        if (!statusCheckResult.equals("OK")) {
            return statusCheckResult;
        }

        int num = getPositionNum(zlid);
        String msg = "[招录简章]：" + (num > 0 ? "共有" + num + "个招录职位待推送!" : "无待推送的职位记录！");
        log.info(msg);

        if (num == 0) {
            return msg;
        }

        // 2. 同步前先清空目标表数据
        clearTargetTables(zlid);

        int rows = 0;
        for (String srcTable : tables.keySet()) {
            String dstTable = tables.get(srcTable);
            int tableRows = pushTable(zlid, srcTable, dstTable);
            if ("ZL_PLAN_POSITION".equalsIgnoreCase(srcTable)) {
                rows = tableRows;
            }
        }

        long costTime = (System.currentTimeMillis() - startTime) / 1000;
        msg = "[招录简章]：成功推送" + rows + "个招录职位，用时" + costTime + "秒!";

        return msg;
    }

    /**
     * 获取中间库待同步的考生数
     * @param zlid
     * @return
     */
    public int getPositionNum(String zlid) {
        String sql = "select count(*) from ZL_PLAN_POSITION where ZLID = ?";
        int count = localJdbcTemplate.queryForObject(sql, Integer.class, zlid);
        return count;
    }

    /**
     * 同步单个表
     * @param zlid
     * @param srcTable 中间库 考生表
     * @param dstTable 内网 考生表
     * @return
     */
    private int pushTable(String zlid, String srcTable, String dstTable) {
        long startTime = System.currentTimeMillis();

        // 同步必填参数
        LocalDateTime makeDate = LocalDateTime.now();
        String lsh = makeDate.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        // 构建SQL - 只同步状态为5的招录单位数据
        String sql;
        Object[] sqlParams;

        if ("ZL_PLAN_POSITION_SUBMIT".equalsIgnoreCase(srcTable)) {
            // 对于上报表，只同步状态为5的记录
            sql = "select *, 0 as TBBZ, ? as MAKEDATE, ? as LSH \n" +
                  "from " + srcTable + "\n" +
                  "where ZLID = ? and ZLPMSTATUS = 5 \n";
            sqlParams = new Object[]{makeDate, lsh, zlid};
        } else if ("ZL_PLAN_POSITION".equalsIgnoreCase(srcTable)) {
            // 对于职位表，只同步对应状态为5的招录单位的职位
            sql = "select p.*, 0 as TBBZ, ? as MAKEDATE, ? as LSH \n" +
                  "from " + srcTable + " p \n" +
                  "inner join ZL_PLAN_POSITION_SUBMIT s on s.ZLID = p.ZLID and s.ZLUID = p.YRUID \n" +
                  "where p.ZLID = ? and s.ZLPMSTATUS = 5 \n";
            sqlParams = new Object[]{makeDate, lsh, zlid};
        } else {
            // 其他表保持原逻辑
            sql = "select *, 0 as TBBZ, ? as MAKEDATE, ? as LSH \n" +
                  "from " + srcTable + "\n where ZLID = ? \n";
            sqlParams = new Object[]{makeDate, lsh, zlid};
        }

        ResultSetExtractor<Integer> loader = new BatchLoaderResultSet(dstTable, getBatchLoader());
        int rows = localJdbcTemplate.query(sql, loader, sqlParams);

        long costTime = (System.currentTimeMillis() - startTime);
        log.info(srcTable + " --> " + dstTable + ": 已同步" + rows + "条记录, 用时" + costTime + "毫秒!");

        return rows;
    }

    /**
     * 检查同步状态
     * @param zlid 招录事项ID
     * @return OK-可以同步，其他-不能同步的原因
     */
    private String checkSyncStatus(String zlid) {
        String sql = "select s.ZLUID, d.DEPT_NAME, s.ZLPMSTATUS " +
                    "from ZL_PLAN_POSITION_SUBMIT s " +
                    "left join SYS_DEPT d on d.DEPT_ID = s.ZLUID " +
                    "where s.ZLID = ? " +
                    "order by s.ZLUID";
        try {
            List<Map<String, Object>> statusList = localJdbcTemplate.queryForList(sql, zlid);

            if (statusList.isEmpty()) {
                return "[招录简章]：未找到招录事项的上报记录，无法同步！";
            }

            // 检查所有招录单位的状态
            List<String> status4Units = new ArrayList<>();  // 状态为4的单位
            List<String> otherStatusUnits = new ArrayList<>();  // 其他状态的单位
            boolean hasStatus5 = false;  // 是否有状态为5的单位

            for (Map<String, Object> row : statusList) {
                Integer status = (Integer) row.get("ZLPMSTATUS");
                String deptName = (String) row.get("DEPT_NAME");
                String unitInfo = deptName != null ? deptName : "单位ID:" + row.get("ZLUID");

                if (status == null) {
                    otherStatusUnits.add(unitInfo + "(状态为空)");
                } else if (status == 4) {
                    status4Units.add(unitInfo);
                } else if (status == 5) {
                    hasStatus5 = true;
                } else {
                    otherStatusUnits.add(unitInfo + "(状态:" + status + ")");
                }
            }

            // 构建返回消息
            StringBuilder message = new StringBuilder();

            if (!status4Units.isEmpty()) {
                message.append("[招录简章]：以下招录单位状态为4（审核中），暂不能同步：")
                       .append(String.join("、", status4Units));
            }

            if (!otherStatusUnits.isEmpty()) {
                if (message.length() > 0) message.append("；");
                message.append("以下招录单位状态不符合同步条件：")
                       .append(String.join("、", otherStatusUnits));
            }

            if (!hasStatus5) {
                if (message.length() > 0) message.append("；");
                message.append("没有状态为5的招录单位，无法同步！");
                return message.toString();
            }

            // 如果有状态4或其他状态的单位，给出提示但允许同步状态5的单位
            if (message.length() > 0) {
                message.append("；将只同步状态为5的招录单位数据！");
                log.warn("招录事项 {} 部分单位状态不符合条件: {}", zlid, message.toString());
            }

            return "OK";

        } catch (Exception e) {
            log.error("检查同步状态失败, zlid: {}", zlid, e);
            return "[招录简章]：检查同步状态失败：" + e.getMessage();
        }
    }

    /**
     * 清空目标表中对应zlid的数据
     * @param zlid 招录事项ID
     */
    private void clearTargetTables(String zlid) {
        try {
            // 清空 ZLPCONTAINER_ZLPOSITION 表
            String sql1 = "delete from ZLPCONTAINER_ZLPOSITION where ZLID = ?";
            int rows1 = remoteJdbcTemplate.update(sql1, zlid);
            log.info("清空目标表 ZLPCONTAINER_ZLPOSITION，删除 {} 条记录", rows1);

            // 清空 ZLPCONTAINER 表
            String sql2 = "delete from ZLPCONTAINER where ZLID = ?";
            int rows2 = remoteJdbcTemplate.update(sql2, zlid);
            log.info("清空目标表 ZLPCONTAINER，删除 {} 条记录", rows2);

        } catch (Exception e) {
            log.error("清空目标表失败, zlid: {}", zlid, e);
            throw new RuntimeException("清空目标表失败：" + e.getMessage());
        }
    }

    private BatchLoader getBatchLoader() {
        if (batchLoader == null) {
            batchLoader = new BatchLoader(remoteJdbcTemplate);
        }
        return batchLoader;
    }
}
