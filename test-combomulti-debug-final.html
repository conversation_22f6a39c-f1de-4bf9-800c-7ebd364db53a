<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>ComboMulti 最终调试测试</title>
  <link rel="stylesheet" href="./webix/webix.css" type="text/css">
  <script src="./webix/webix.js"></script>
  <script src="./webix/i18n/zh.js"></script>
  <!-- 注意：实际项目中应该正确引入WebixSearchUtil -->
</head>
<body>
  <div id="search_container" style="margin: 20px;"></div>
  <div id="debug_container" style="margin: 20px;"></div>

  <script>
    document.addEventListener("DOMContentLoaded", function() {
      webix.i18n.setLocale("zh-CN");

      // 模拟搜索配置（与实际项目一致）
      const searchConfig = [
        {
          id: "ZL_PLAN__ZLSTATUS",
          label: "状态",
          view: "combomulti",
          placeholder: "请选择状态",
          inputWidth: 200,
          searchLevel: 1,
          options: [
            { id: "01", value: "起草" },
            { id: "02", value: "流转" },
            { id: "03", value: "核准" },
            { id: "04", value: "驳回" },
            { id: "05", value: "撤销" }
          ]
        },
        {
          id: "ZL_PLAN__ZLYEAR",
          label: "年份",
          view: "text",
          placeholder: "请输入年份",
          inputWidth: 120,
          searchLevel: 1
        }
      ];

      // 使用WebixSearchUtil构建搜索UI
      const searchUI = WebixSearchUtil.buildSearchUI(searchConfig, {
        columnsPerRow: 3,
        onSearch: function(filter) {
          console.log("🔍 [测试] 搜索回调被调用");
          console.log("🔍 [测试] 过滤器:", filter);
          
          // 显示结果
          $$("debug_display").setHTML(
            "<h3>搜索结果：</h3>" +
            "<pre>" + JSON.stringify(filter, null, 2) + "</pre>" +
            "<h4>检查点：</h4>" +
            "<ul>" +
            "<li>字段名是否包含_IN？" + (Object.keys(filter).some(k => k.includes('_IN')) ? "<span style='color:red'>是</span>" : "<span style='color:green'>否</span>") + "</li>" +
            "<li>状态值类型：" + (filter["ZL_PLAN__ZLSTATUS"] ? (Array.isArray(filter["ZL_PLAN__ZLSTATUS"]) ? "数组" : typeof filter["ZL_PLAN__ZLSTATUS"]) : "无值") + "</li>" +
            "<li>状态值内容：" + (filter["ZL_PLAN__ZLSTATUS"] ? JSON.stringify(filter["ZL_PLAN__ZLSTATUS"]) : "无值") + "</li>" +
            "</ul>"
          );
        }
      });

      // 创建搜索表单
      webix.ui({
        container: "search_container",
        rows: [
          { view: "label", label: "ComboMulti 调试测试 - 请打开浏览器控制台查看详细日志", css: "webix_header" },
          {
            view: "form",
            id: "search_form",
            elements: [
              {
                cols: searchUI.defaultUI
              },
              {
                cols: [
                  {
                    view: "button",
                    value: "手动设置测试值",
                    width: 150,
                    click: function() {
                      console.log("🔍 [测试] 手动设置测试值");
                      // 设置代码值
                      $$("default_ZL_PLAN__ZLSTATUS").setValue("01,02,03");
                      $$("default_ZL_PLAN__ZLYEAR").setValue("2025");
                      console.log("🔍 [测试] 设置完成");
                    }
                  },
                  {
                    view: "button",
                    value: "获取当前值",
                    width: 120,
                    click: function() {
                      console.log("🔍 [测试] 获取当前值");
                      const statusValue = $$("default_ZL_PLAN__ZLSTATUS").getValue();
                      const yearValue = $$("default_ZL_PLAN__ZLYEAR").getValue();
                      
                      console.log("🔍 [测试] 状态值:", statusValue);
                      console.log("🔍 [测试] 年份值:", yearValue);
                      
                      $$("debug_display").setHTML(
                        "<h3>当前表单值：</h3>" +
                        "<p><strong>状态：</strong>" + (statusValue || "无") + "</p>" +
                        "<p><strong>年份：</strong>" + (yearValue || "无") + "</p>"
                      );
                    }
                  },
                  {
                    view: "button",
                    value: "清空",
                    width: 80,
                    click: function() {
                      console.log("🔍 [测试] 清空表单");
                      $$("default_ZL_PLAN__ZLSTATUS").setValue("");
                      $$("default_ZL_PLAN__ZLYEAR").setValue("");
                    }
                  }
                ]
              }
            ]
          }
        ]
      });

      webix.ui({
        container: "debug_container",
        view: "template",
        id: "debug_display",
        template: "<h3>调试信息将显示在这里</h3><p>请：</p><ol><li>打开浏览器控制台</li><li>点击状态字段进行多选</li><li>点击搜索按钮</li><li>查看控制台中的 [ComboMulti] 调试信息</li></ol>",
        height: 300
      });

      // 添加一些测试说明
      console.log("🔍 [测试] 页面加载完成");
      console.log("🔍 [测试] 搜索配置:", searchConfig);
      console.log("🔍 [测试] 请进行以下测试：");
      console.log("🔍 [测试] 1. 点击状态字段，选择多个选项");
      console.log("🔍 [测试] 2. 点击确定");
      console.log("🔍 [测试] 3. 点击搜索按钮");
      console.log("🔍 [测试] 4. 查看控制台中的 [ComboMulti] 调试信息");
    });
  </script>
</body>
</html>
