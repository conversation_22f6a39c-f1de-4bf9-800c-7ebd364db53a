<!DOCTYPE html>
<html>
<head>
    <title>简化调试测试</title>
    <script type="text/javascript" src="https://cdn.webix.com/edge/webix.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.webix.com/edge/webix.css">
    <style>
        body { margin: 20px; font-family: Arial, sans-serif; }
        .debug-info { 
            background: #f0f8ff; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
            border: 1px solid #007acc;
        }
        .result-info { 
            background: #f0fff0; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
            border: 1px solid #28a745;
        }
        pre { 
            background: white; 
            padding: 10px; 
            border-radius: 3px; 
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
    </style>
</head>
<body>
    <h1>WebixSearchUtil 调试测试</h1>
    
    <div class="debug-info">
        <h3>测试说明</h3>
        <p>这个页面用于测试修复后的字段值获取问题。请按以下步骤操作：</p>
        <ol>
            <li>打开浏览器控制台查看详细日志</li>
            <li>在搜索框中输入一些值</li>
            <li>点击"检索"按钮</li>
            <li>观察控制台输出和下方的结果显示</li>
        </ol>
    </div>

    <div id="searchContainer"></div>
    
    <div style="margin: 20px 0;">
        <button onclick="testGetFieldValues()">测试获取字段值</button>
        <button onclick="testBuildFilter()">测试构建过滤器</button>
        <button onclick="clearResults()">清空结果</button>
    </div>
    
    <div id="resultContainer"></div>

    <script>
        // 简化的 WebixSearchUtil 核心方法（包含修复）
        const WebixSearchUtil = {
            CONSTANTS: {
                ALL_VALUE: "-99",
                VIEWS: {
                    COMBO: "combo",
                    TEXT: "text",
                    DATE: "datepicker",
                    DATERANGE: "daterangepicker",
                    NUMBER: "number"
                },
                SEARCH_LEVEL: {
                    DEFAULT: 1,
                    ADVANCED: 2
                }
            },

            currentOptions: null,

            // 获取字段值（修复版本）
            getFieldValue: function(fieldId, prefix = "default_") {
                console.log(`📖 [getFieldValue] 获取字段值: ${fieldId}`);
                console.log("🏷️ 前缀:", prefix);

                const componentId = prefix + fieldId;
                console.log("🆔 组件ID:", componentId);

                const input = $$(componentId);
                console.log("🔍 找到组件:", !!input);
                
                if (input) {
                    console.log("🔍 组件详细信息:", {
                        id: input.config ? input.config.id : 'unknown',
                        view: input.config ? input.config.view : 'unknown',
                        hasGetValue: typeof input.getValue === "function"
                    });
                }

                // 检查是否是有效的Webix组件
                if (input && typeof input.getValue === "function") {
                    console.log("✅ 组件有效，开始获取值...");
                    const value = input.getValue();
                    console.log(`📝 获取到的值:`, value, `(类型: ${typeof value})`);
                    console.log(`✅ [getFieldValue] 字段 ${fieldId} 值获取完成:`, value);
                    return value;
                }

                console.log(`❌ [getFieldValue] 未找到有效组件: ${componentId}`);
                return null;
            },

            // 构建过滤器（修复版本）
            buildFilter: function(searchConfig, values) {
                console.log("🔍 [buildFilter] 开始构建过滤器");
                console.log("📋 搜索配置:", searchConfig);
                console.log("📝 输入值:", values);

                const filter = {};
                if (!searchConfig || !Array.isArray(searchConfig)) {
                    console.warn("❌ 搜索配置无效或不是数组");
                    return filter;
                }

                console.log("🔄 开始处理每个字段...");
                searchConfig.forEach((field, index) => {
                    const value = values[field.id];
                    console.log(`📝 处理字段 ${index + 1}/${searchConfig.length}: ${field.id} = ${value} (${field.view})`);

                    if (field.view === this.CONSTANTS.VIEWS.COMBO) {
                        if (value && value !== this.CONSTANTS.ALL_VALUE) {
                            filter[field.id] = value;
                        }
                    } else if (value && value.toString().trim() !== "") {
                        filter[field.id] = value;
                    }
                });

                console.log("🎉 [buildFilter] 过滤器构建完成");
                console.log("📊 过滤器统计:", {
                    totalFields: searchConfig.length,
                    processedValues: Object.keys(values).length,
                    filterConditions: Object.keys(filter).length,
                    filter: filter
                });
                
                return filter;
            },

            // 处理搜索操作（修复版本）
            _handleSearch: function(options, fields) {
                console.log("🔍 [_handleSearch] 开始处理搜索操作");
                console.log("📋 字段数量:", fields ? fields.length : 0);
                console.log("📋 字段列表:", fields ? fields.map(f => f.id) : []);
                
                if (!options.onSearch) {
                    console.warn("❌ 没有提供 onSearch 回调函数");
                    return;
                }

                const values = {};
                console.log("🔄 开始获取字段值...");
                
                fields.forEach((field, index) => {
                    console.log(`📝 获取字段 ${index + 1}/${fields.length}: ${field.id}`);
                    const value = this.getFieldValue(field.id, "default_");
                    console.log(`📝 字段 ${field.id} 的值:`, value, `(类型: ${typeof value})`);
                    
                    if (value !== null && value !== undefined) {
                        values[field.id] = value;
                        console.log(`✅ 字段 ${field.id} 值已添加到搜索条件`);
                    } else {
                        console.log(`⚠️ 字段 ${field.id} 值为空，跳过`);
                    }
                });

                console.log("📝 最终搜索值:", values);
                const filter = this.buildFilter(fields, values);
                console.log("🔍 构建的过滤器:", filter);
                options.onSearch(filter);
            }
        };

        // 测试配置
        const testFields = [
            {
                view: 'text',
                searchLevel: 1,
                id: 'ZL_PLAN__ZLTITLE',
                label: '招录名称',
                placeholder: '请输入招录名称'
            },
            {
                view: 'combo',
                searchLevel: 1,
                options: [
                    { id: '-99', value: '全部' },
                    { id: '1', value: '进行中' },
                    { id: '2', value: '已结束' }
                ],
                id: 'ZL_PLAN__ZLSTATUS',
                label: '状态',
                value: '-99'
            }
        ];

        const testOptions = {
            onSearch: function(filter) {
                console.log("🎯 [测试] 搜索回调被调用，过滤器:", filter);
                showResult("搜索结果", filter);
            }
        };

        // 测试函数
        function testGetFieldValues() {
            console.log("🧪 [测试] 开始测试获取字段值");
            const values = {};
            testFields.forEach(field => {
                const value = WebixSearchUtil.getFieldValue(field.id, "default_");
                values[field.id] = value;
            });
            showResult("字段值获取测试", values);
        }

        function testBuildFilter() {
            console.log("🧪 [测试] 开始测试构建过滤器");
            const values = {};
            testFields.forEach(field => {
                const value = WebixSearchUtil.getFieldValue(field.id, "default_");
                if (value !== null && value !== undefined) {
                    values[field.id] = value;
                }
            });
            const filter = WebixSearchUtil.buildFilter(testFields, values);
            showResult("过滤器构建测试", { values, filter });
        }

        function showResult(title, data) {
            const resultHtml = `
                <div class="result-info">
                    <h3>${title}</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            document.getElementById('resultContainer').innerHTML += resultHtml;
        }

        function clearResults() {
            document.getElementById('resultContainer').innerHTML = '';
        }

        // 初始化界面
        webix.ready(function() {
            console.log("🚀 [测试] 开始初始化界面");
            
            webix.ui({
                container: "searchContainer",
                rows: [
                    {
                        view: "form",
                        elements: [
                            {
                                cols: [
                                    { view: "label", label: "招录名称", width: 80, align: "right" },
                                    { view: "text", id: "default_ZL_PLAN__ZLTITLE", placeholder: "请输入招录名称", width: 200 },
                                    { view: "label", label: "状态", width: 80, align: "right" },
                                    { 
                                        view: "combo", 
                                        id: "default_ZL_PLAN__ZLSTATUS", 
                                        options: [
                                            { id: '-99', value: '全部' },
                                            { id: '1', value: '进行中' },
                                            { id: '2', value: '已结束' }
                                        ],
                                        value: '-99',
                                        width: 200 
                                    },
                                    {
                                        view: "button",
                                        value: "检索",
                                        width: 80,
                                        click: function() {
                                            WebixSearchUtil._handleSearch(testOptions, testFields);
                                        }
                                    }
                                ]
                            }
                        ]
                    }
                ]
            });
            
            console.log("✅ [测试] 界面初始化完成");
        });
    </script>
</body>
</html>
