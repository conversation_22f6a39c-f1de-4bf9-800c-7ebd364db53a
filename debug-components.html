<!DOCTYPE html>
<html>
<head>
    <title>组件调试</title>
    <script type="text/javascript" src="https://cdn.webix.com/edge/webix.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.webix.com/edge/webix.css">
    <style>
        body { margin: 20px; font-family: Arial, sans-serif; }
        .debug-panel { 
            background: #f5f5f5; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
        }
        pre { 
            background: white; 
            padding: 10px; 
            border-radius: 3px; 
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>组件调试 - 检查组件是否正确创建</h1>
    
    <div class="debug-panel">
        <h3>调试说明</h3>
        <p>这个页面用于调试组件创建问题，检查日期范围字段的组件是否正确生成。</p>
    </div>

    <div id="searchContainer"></div>
    <div id="debugInfo"></div>

    <script>
        // 简化的测试配置
        const searchConfig = [
            {
                view: 'daterangepicker',
                searchLevel: 1,
                format: '%Y-%m-%d',
                timepicker: false,
                inputWidth: 300,
                label: '日期范围',
                labelWidth: 80,
                id: 'ZL_PLAN__BMSTARTDATE',
                placeholder: '请选择日期范围'
            },
            {
                view: 'text',
                searchLevel: 1,
                labelWidth: 80,
                id: 'ZL_PLAN__ZLLSH',
                label: '编号',
                placeholder: '请输入编号'
            }
        ];

        const searchOptions = {
            columnsPerRow: 1,
            showButtons: true,
            onSearch: function(filter) {
                console.log("🔍 搜索回调:", filter);
                
                // 检查组件是否存在
                const dateStart = $$("default_ZL_PLAN__BMSTARTDATE#1");
                const dateEnd = $$("default_ZL_PLAN__BMSTARTDATE#2");
                const textField = $$("default_ZL_PLAN__ZLLSH");
                
                let debugHtml = `
                    <div class="debug-panel">
                        <h3>组件检查结果</h3>
                        <p><strong>default_ZL_PLAN__BMSTARTDATE#1:</strong> ${dateStart ? '✅ 存在' : '❌ 不存在'}</p>
                        <p><strong>default_ZL_PLAN__BMSTARTDATE#2:</strong> ${dateEnd ? '✅ 存在' : '❌ 不存在'}</p>
                        <p><strong>default_ZL_PLAN__ZLLSH:</strong> ${textField ? '✅ 存在' : '❌ 不存在'}</p>
                        
                        <h4>所有 Webix 组件列表:</h4>
                        <pre>${JSON.stringify(Object.keys(webix.ui.views), null, 2)}</pre>
                        
                        <h4>搜索结果:</h4>
                        <pre>${JSON.stringify(filter, null, 2)}</pre>
                    </div>
                `;
                
                document.getElementById('debugInfo').innerHTML = debugHtml;
            }
        };

        // 页面加载完成后初始化
        webix.ready(function() {
            console.log("🚀 开始加载 WebixSearchUtil...");
            
            fetch('doc/2.txt')
                .then(response => response.text())
                .then(code => {
                    console.log("📁 WebixSearchUtil 代码加载完成");
                    eval(code);
                    
                    console.log("🔧 构建搜索UI...");
                    const searchUI = WebixSearchUtil.buildSearchUI(searchConfig, searchOptions);
                    
                    console.log("🔍 生成的搜索UI结构:", searchUI);
                    
                    webix.ui({
                        container: "searchContainer",
                        rows: [
                            {
                                view: "toolbar",
                                elements: [
                                    { view: "label", label: "组件调试界面" }
                                ]
                            },
                            {
                                view: "form",
                                elements: searchUI.defaultUI
                            }
                        ]
                    });
                    
                    console.log("✅ 界面创建完成");
                    
                    // 延迟检查组件
                    setTimeout(() => {
                        console.log("🔍 检查创建的组件...");
                        console.log("Webix views:", Object.keys(webix.ui.views));
                        
                        const dateStart = $$("default_ZL_PLAN__BMSTARTDATE#1");
                        const dateEnd = $$("default_ZL_PLAN__BMSTARTDATE#2");
                        const textField = $$("default_ZL_PLAN__ZLLSH");
                        
                        console.log("default_ZL_PLAN__BMSTARTDATE#1:", dateStart);
                        console.log("default_ZL_PLAN__BMSTARTDATE#2:", dateEnd);
                        console.log("default_ZL_PLAN__ZLLSH:", textField);
                    }, 1000);
                })
                .catch(error => {
                    console.error("❌ 加载失败:", error);
                    document.getElementById('debugInfo').innerHTML = 
                        `<div class="debug-panel"><h3>加载错误</h3><p>${error.message}</p></div>`;
                });
        });
    </script>
</body>
</html>
