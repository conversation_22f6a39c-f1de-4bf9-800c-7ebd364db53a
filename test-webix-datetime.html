<!DOCTYPE html>
<html>
<head>
    <title>Webix 日期时间选择器直接测试</title>
    <script type="text/javascript" src="https://cdn.webix.com/edge/webix.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.webix.com/edge/webix.css">
    <style>
        body { margin: 20px; font-family: Arial, sans-serif; }
        .test-panel { 
            background: #f0f8ff; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Webix 日期时间选择器直接测试</h1>
    
    <div class="test-panel">
        <h3>测试说明</h3>
        <p>这个页面直接使用 Webix API 创建日期选择器，验证时间选择器功能。</p>
    </div>

    <div id="testContainer"></div>

    <script>
        webix.ready(function() {
            console.log("🚀 开始创建 Webix 日期选择器测试");
            
            webix.ui({
                container: "testContainer",
                rows: [
                    {
                        view: "toolbar",
                        elements: [
                            { view: "label", label: "Webix 日期时间选择器直接测试" }
                        ]
                    },
                    {
                        view: "form",
                        elements: [
                            {
                                cols: [
                                    { view: "label", label: "纯日期:", width: 100, align: "right" },
                                    {
                                        view: "datepicker",
                                        id: "date_only",
                                        format: "%Y-%m-%d",
                                        timepicker: false,
                                        stringResult: true,
                                        width: 200
                                    }
                                ]
                            },
                            { height: 10 },
                            {
                                cols: [
                                    { view: "label", label: "日期时间(datepicker):", width: 120, align: "right" },
                                    {
                                        view: "datepicker",
                                        id: "datetime_picker",
                                        format: "%Y-%m-%d %H:%i",
                                        timepicker: true,
                                        stringResult: true,
                                        width: 200
                                    }
                                ]
                            },
                            { height: 10 },
                            {
                                cols: [
                                    { view: "label", label: "时间选择器:", width: 120, align: "right" },
                                    {
                                        view: "timepicker",
                                        id: "time_control",
                                        format: "%H:%i",
                                        stringResult: true,
                                        width: 200
                                    }
                                ]
                            },
                            { height: 10 },
                            {
                                cols: [
                                    { view: "label", label: "日期范围:", width: 100, align: "right" },
                                    {
                                        cols: [
                                            {
                                                view: "datepicker",
                                                id: "date_range_start",
                                                format: "%Y-%m-%d",
                                                timepicker: false,
                                                stringResult: true,
                                                placeholder: "开始日期",
                                                width: 150
                                            },
                                            { view: "label", label: "~", width: 20 },
                                            {
                                                view: "datepicker",
                                                id: "date_range_end",
                                                format: "%Y-%m-%d",
                                                timepicker: false,
                                                stringResult: true,
                                                placeholder: "结束日期",
                                                width: 150
                                            }
                                        ]
                                    }
                                ]
                            },
                            { height: 10 },
                            {
                                cols: [
                                    { view: "label", label: "日期时间范围:", width: 100, align: "right" },
                                    {
                                        cols: [
                                            {
                                                view: "datepicker",
                                                id: "datetime_range_start",
                                                format: "%Y-%m-%d %H:%i",
                                                timepicker: true,
                                                stringResult: true,
                                                placeholder: "开始日期时间",
                                                width: 180
                                            },
                                            { view: "label", label: "~", width: 20 },
                                            {
                                                view: "datepicker",
                                                id: "datetime_range_end",
                                                format: "%Y-%m-%d %H:%i",
                                                timepicker: true,
                                                stringResult: true,
                                                placeholder: "结束日期时间",
                                                width: 180
                                            }
                                        ]
                                    }
                                ]
                            },
                            { height: 20 },
                            {
                                cols: [
                                    {},
                                    {
                                        view: "button",
                                        value: "检查配置",
                                        width: 100,
                                        click: function() {
                                            const dateOnly = $$("date_only");
                                            const datetimePicker = $$("datetime_picker");
                                            const timeControl = $$("time_control");
                                            const datetimeStart = $$("datetime_range_start");

                                            console.log("📅 纯日期组件配置:", {
                                                view: dateOnly.config.view,
                                                timepicker: dateOnly.config.timepicker,
                                                format: dateOnly.config.format
                                            });

                                            console.log("📅 日期时间(datepicker)组件配置:", {
                                                view: datetimePicker.config.view,
                                                timepicker: datetimePicker.config.timepicker,
                                                format: datetimePicker.config.format
                                            });

                                            console.log("📅 日期时间(datetime)组件配置:", {
                                                view: datetimeControl.config.view,
                                                format: datetimeControl.config.format
                                            });

                                            console.log("📅 日期时间范围开始组件配置:", {
                                                view: datetimeStart.config.view,
                                                timepicker: datetimeStart.config.timepicker,
                                                format: datetimeStart.config.format
                                            });

                                            webix.message("配置信息已输出到控制台");
                                        }
                                    },
                                    {
                                        view: "button",
                                        value: "获取值",
                                        width: 100,
                                        click: function() {
                                            const values = {
                                                dateOnly: $$("date_only").getValue(),
                                                datetimePicker: $$("datetime_picker").getValue(),
                                                datetimeControl: $$("datetime_control").getValue(),
                                                dateRangeStart: $$("date_range_start").getValue(),
                                                dateRangeEnd: $$("date_range_end").getValue(),
                                                datetimeRangeStart: $$("datetime_range_start").getValue(),
                                                datetimeRangeEnd: $$("datetime_range_end").getValue()
                                            };
                                            
                                            console.log("📝 所有字段的值:", values);
                                            webix.message("字段值已输出到控制台");
                                        }
                                    }
                                ]
                            }
                        ]
                    }
                ]
            });
            
            console.log("✅ Webix 日期选择器测试界面创建完成");
        });
    </script>
</body>
</html>
