@Override
@Transactional
public boolean updateRowData(String zlpId, Map<String, Object> input) {
    try {
        String ZLPID = zlpId;
        
        // 先查询原有数据，用于保持未传递字段的原值
        String selectSql = "SELECT * FROM ZL_PLAN_POSITION WHERE ZLPID = ?";
        Map<String, Object> existingData = jdbcTemplate.queryForMap(selectSql, ZLPID);
        
        // 构建更新数据 - 只更新传递了的字段
        Map<String, Object> updateData = new HashMap<>();
        
        // 检查是否传递了用人单位相关数据
        boolean hasYruidData = input.containsKey("ZL_PLAN_POSITION__YRUID") && 
                               input.get("ZL_PLAN_POSITION__YRUID") != null &&
                               !"null".equalsIgnoreCase(String.valueOf(input.get("ZL_PLAN_POSITION__YRUID"))) &&
                               !String.valueOf(input.get("ZL_PLAN_POSITION__YRUID")).trim().isEmpty() &&
                               !"0".equals(String.valueOf(input.get("ZL_PLAN_POSITION__YRUID")));
        
        // 检查是否传递了子部门数据
        boolean hasSubDeptData = input.containsKey("ZL_PLAN_POSITION__SUB_DEPT_DATA") &&
                                 input.get("ZL_PLAN_POSITION__SUB_DEPT_DATA") != null &&
                                 !"null".equalsIgnoreCase(String.valueOf(input.get("ZL_PLAN_POSITION__SUB_DEPT_DATA"))) &&
                                 !String.valueOf(input.get("ZL_PLAN_POSITION__SUB_DEPT_DATA")).trim().isEmpty();
        
        // 只有传递了用人单位数据才处理用人单位相关字段
        if (hasYruidData) {
            Long YRUID = Long.valueOf(String.valueOf(input.get("ZL_PLAN_POSITION__YRUID")));
            Dept dept = deptService.selectDeptById(YRUID);
            
            if (dept != null) {
                String YRUNITNAME = dept.getDeptName();
                String YRDEPNAME = deptService.getFromHostToDeptNamePath(YRUID);
                String YRDEPNAME2 = input.containsKey("ZL_PLAN_POSITION__YRDEPNAME2") ? 
                    String.valueOf(input.get("ZL_PLAN_POSITION__YRDEPNAME2")) : "";
                
                updateData.put("YRUID", YRUID);
                updateData.put("YRUNITNAME", YRUNITNAME);
                updateData.put("YRDEPNAME", YRDEPNAME);
                updateData.put("YRDEPNAME2", YRDEPNAME2);
                
                // 处理子部门数据
                StringBuilder yrunitname2Builder = new StringBuilder();
                
                if (hasSubDeptData) {
                    // 先删除原有的子部门数据
                    jdbcTemplate.update("DELETE FROM zl_plan_position_yrunit WHERE ZLPID = ?", ZLPID);
                    
                    String subDeptDataJson = String.valueOf(input.get("ZL_PLAN_POSITION__SUB_DEPT_DATA"));
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        List<Map<String, Object>> subDeptList = objectMapper.readValue(subDeptDataJson,
                                objectMapper.getTypeFactory().constructCollectionType(List.class, Map.class));
                        
                        // 构建合并部门名称
                        for (int i = 0; i < subDeptList.size(); i++) {
                            Map<String, Object> subDept = subDeptList.get(i);
                            String deptName = String.valueOf(subDept.get("deptName"));
                            String number = String.valueOf(subDept.get("number"));
                            
                            if (i > 0) yrunitname2Builder.append("<br>");
                            
                            if (number != null && !"null".equals(number)) {
                                yrunitname2Builder.append(deptName).append(":").append(number).append("人");
                            } else {
                                yrunitname2Builder.append(deptName);
                            }
                        }
                        
                        // 获取GWLB用于子部门表
                        Long GWLB = 0L;
                        if (input.containsKey("ZL_PLAN_POSITION__GWLB")) {
                            Object gwlbObj = input.get("ZL_PLAN_POSITION__GWLB");
                            if (gwlbObj != null && !"null".equalsIgnoreCase(String.valueOf(gwlbObj))) {
                                GWLB = Long.valueOf(String.valueOf(gwlbObj));
                            }
                        }
                        
                        // 插入新的子部门数据
                        for (Map<String, Object> subDept : subDeptList) {
                            String deptId = String.valueOf(subDept.get("deptId"));
                            String deptName = String.valueOf(subDept.get("deptName"));
                            String number = String.valueOf(subDept.get("number"));
                            String sort = String.valueOf(subDept.get("sort"));
                            
                            jdbcTemplate.update(
                                    "INSERT INTO zl_plan_position_yrunit (ZLPID, YRUNITTYPE, YRUNITTYPENAME, UPXZ, UNUMBER, SEQ) VALUES (?, ?, ?, ?, ?, ?)",
                                    ZLPID, Long.valueOf(deptId), deptName, GWLB, Long.valueOf(number), Integer.valueOf(sort)
                            );
                        }
                    } catch (Exception e) {
                        log.error("解析子部门数据失败", e);
                        return false;
                    }
                }
                
                String YRUNITNAME2 = yrunitname2Builder.toString();
                updateData.put("YRUNITNAME2", YRUNITNAME2);
                
                // 构建显示字段 YRDEPNAME_SHOW
                StringBuilder YRDEPNAME_SHOW = new StringBuilder();
                if (YRDEPNAME2 != null && !YRDEPNAME2.trim().isEmpty()) {
                    YRDEPNAME_SHOW.append(YRDEPNAME2).append("（合并部门）");
                    if (YRUNITNAME2 != null && !YRUNITNAME2.trim().isEmpty()) {
                        YRDEPNAME_SHOW.append("<br>").append(YRUNITNAME2);
                    }
                } else if (YRUNITNAME2 != null && !YRUNITNAME2.trim().isEmpty()) {
                    YRDEPNAME_SHOW.append(YRUNITNAME2);
                }
                updateData.put("YRDEPNAME_SHOW", YRDEPNAME_SHOW.toString());
            }
        }
        
        // 处理其他字段 - 只更新传递了的字段
        if (input.containsKey("ZL_PLAN_POSITION__ZLUID")) {
            Object zluidObj = input.get("ZL_PLAN_POSITION__ZLUID");
            if (zluidObj != null && !"null".equalsIgnoreCase(String.valueOf(zluidObj))) {
                updateData.put("ZLUID", Long.valueOf(String.valueOf(zluidObj)));
            }
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__ISJLXS")) {
            Object isjlxObj = input.get("ZL_PLAN_POSITION__ISJLXS");
            if (isjlxObj != null && !"null".equalsIgnoreCase(String.valueOf(isjlxObj))) {
                updateData.put("ISJLXS", Long.valueOf(String.valueOf(isjlxObj)));
            }
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__POS_ID")) {
            String POSID = String.valueOf(input.get("ZL_PLAN_POSITION__POS_ID"));
            if (!"null".equals(POSID) && !POSID.trim().isEmpty()) {
                DictData POSID_DictData = dictDataService.selectDictDataByTypeAndValue("csras_zlposition_posname", POSID);
                String POSNAME = POSID_DictData != null ? POSID_DictData.getDictAlias() : "";
                updateData.put("POS_ID", POSID);
                updateData.put("POS_NAME", POSNAME);
            }
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__UPDETAIL")) {
            String UPDETAIL = String.valueOf(input.get("ZL_PLAN_POSITION__UPDETAIL"));
            if (!"null".equals(UPDETAIL)) {
                updateData.put("UPDETAIL", UPDETAIL);
            }
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__GWLB")) {
            Object gwlbObj = input.get("ZL_PLAN_POSITION__GWLB");
            if (gwlbObj != null && !"null".equalsIgnoreCase(String.valueOf(gwlbObj))) {
                updateData.put("GWLB", Long.valueOf(String.valueOf(gwlbObj)));
            }
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__ZLRYLX")) {
            Object zlrylxObj = input.get("ZL_PLAN_POSITION__ZLRYLX");
            if (zlrylxObj != null && !"null".equalsIgnoreCase(String.valueOf(zlrylxObj))) {
                updateData.put("ZLRYLX", Long.valueOf(String.valueOf(zlrylxObj)));
            }
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__ZKOBJECT")) {
            updateData.put("ZKOBJECT", String.valueOf(input.get("ZL_PLAN_POSITION__ZKOBJECT")));
        }
        
        // 处理专业要求相关字段
        if (input.containsKey("ZL_PLAN_POSITION__ZHUANYE_BKCKID_DMBNUM")) {
            String ZHUANYE_BKCKID_DMBNUM = String.valueOf(input.get("ZL_PLAN_POSITION__ZHUANYE_BKCKID_DMBNUM"));
            String ZHUANYE_BKCK = "";
            if (ZHUANYE_BKCKID_DMBNUM != null && !ZHUANYE_BKCKID_DMBNUM.trim().isEmpty() && !"null".equals(ZHUANYE_BKCKID_DMBNUM)) {
                ZHUANYE_BKCK = dictDataService.selectDictLabelsByTypeAndValues("GWY009", ZHUANYE_BKCKID_DMBNUM);
            }
            updateData.put("ZHUANYE_BKCK", ZHUANYE_BKCK);
            updateData.put("ZHUANYE_BKCKID", ZHUANYE_BKCKID_DMBNUM);
            updateData.put("ZHUANYE_BKCKID_DMBNUM", ZHUANYE_BKCKID_DMBNUM);
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__ZHUANYE_YJSCKID_DMBNUM")) {
            String ZHUANYE_YJSCKID_DMBNUM = String.valueOf(input.get("ZL_PLAN_POSITION__ZHUANYE_YJSCKID_DMBNUM"));
            String ZHUANYE_YJSCK = "";
            if (ZHUANYE_YJSCKID_DMBNUM != null && !ZHUANYE_YJSCKID_DMBNUM.trim().isEmpty() && !"null".equals(ZHUANYE_YJSCKID_DMBNUM)) {
                ZHUANYE_YJSCK = dictDataService.selectDictLabelsByTypeAndValues("GWY015", ZHUANYE_YJSCKID_DMBNUM);
            }
            updateData.put("ZHUANYE_YJSCK", ZHUANYE_YJSCK);
            updateData.put("ZHUANYE_YJSCKID", ZHUANYE_YJSCKID_DMBNUM);
            updateData.put("ZHUANYE_YJSCKID_DMBNUM", ZHUANYE_YJSCKID_DMBNUM);
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__ZHUANYE_YQ")) {
            Object zyyqObj = input.get("ZL_PLAN_POSITION__ZHUANYE_YQ");
            Long ZHUANYE_YQ = 0L;
            if (zyyqObj != null && !"null".equalsIgnoreCase(String.valueOf(zyyqObj))) {
                String zyyqStr = String.valueOf(zyyqObj).trim();
                if (!zyyqStr.isEmpty() && !"null".equalsIgnoreCase(zyyqStr)) {
                    ZHUANYE_YQ = Long.valueOf(zyyqStr);
                }
            }
            updateData.put("ZHUANYE_YQ", ZHUANYE_YQ);
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__XUELI")) {
            updateData.put("XUELI", String.valueOf(input.get("ZL_PLAN_POSITION__XUELI")));
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__XUEWEI")) {
            updateData.put("XUEWEI", String.valueOf(input.get("ZL_PLAN_POSITION__XUEWEI")));
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__POLITICAL")) {
            updateData.put("POLITICAL", String.valueOf(input.get("ZL_PLAN_POSITION__POLITICAL")));
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__POS_SEX")) {
            updateData.put("POS_SEX", String.valueOf(input.get("ZL_PLAN_POSITION__POS_SEX")));
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__BEIZHUTYPE")) {
            updateData.put("BEIZHUTYPE", String.valueOf(input.get("ZL_PLAN_POSITION__BEIZHUTYPE")));
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__OTHER")) {
            updateData.put("OTHER", String.valueOf(input.get("ZL_PLAN_POSITION__OTHER")));
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__ISXZGWY")) {
            Object isxzgwyObj = input.get("ZL_PLAN_POSITION__ISXZGWY");
            if (isxzgwyObj != null && !"null".equalsIgnoreCase(String.valueOf(isxzgwyObj))) {
                updateData.put("ISXZGWY", Long.valueOf(String.valueOf(isxzgwyObj)));
            }
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__UNUMBER")) {
            Object unumberObj = input.get("ZL_PLAN_POSITION__UNUMBER");
            if (unumberObj != null && !"null".equalsIgnoreCase(String.valueOf(unumberObj))) {
                String unumberStr = String.valueOf(unumberObj).trim();
                if (!unumberStr.isEmpty()) {
                    updateData.put("UNUMBER", Long.valueOf(unumberStr));
                }
            }
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__OTHERBEIZHU")) {
            updateData.put("OTHERBEIZHU", String.valueOf(input.get("ZL_PLAN_POSITION__OTHERBEIZHU")));
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__YXTJ")) {
            updateData.put("YXTJ", String.valueOf(input.get("ZL_PLAN_POSITION__YXTJ")));
        }
        
        if (input.containsKey("ZL_PLAN_POSITION__BEIZHU")) {
            String BEIZHU = String.valueOf(input.get("ZL_PLAN_POSITION__BEIZHU"));
            if ("null".equalsIgnoreCase(BEIZHU)) {
                BEIZHU = "";
            }
            updateData.put("BEIZHU", BEIZHU);
        }
        
        // 如果没有要更新的字段，直接返回成功
        if (updateData.isEmpty()) {
            log.info("没有字段需要更新: ZLPID={}", ZLPID);
            return true;
        }
        
        // 添加更新时间等字段
        updateData.put("UPNAME", "手动更新");
        updateData.put("NOYEAR", Long.valueOf(java.time.Year.now().getValue()));

        // 处理日志记录（保持原有逻辑）
        // 查询原始数据（用于日志记录）
        for (Map.Entry<String, Object> entry : existingData.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof BigDecimal) {
                BigDecimal bd = (BigDecimal) value;
                // 检查是否有小数部分
                if (bd.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
                    entry.setValue(bd.intValue());
                } else {
                    log.warn("字段 {} 有小数部分，保持 BigDecimal 类型: {}", entry.getKey(), bd);
                }
            }
        }

        // 判断REJECT并记录日志
        Object rejectVal = existingData.get("REJECT");
        boolean isRejected = false;
        if (rejectVal != null) {
            if (rejectVal instanceof Number) {
                isRejected = ((Number) rejectVal).intValue() > 0;
            } else {
                try {
                    isRejected = Integer.parseInt(rejectVal.toString()) > 0;
                } catch (Exception ignore) {}
            }
        }

        // 只有已驳回的才记录日志
        if (isRejected) {
            for (Map.Entry<String, Object> entry : updateData.entrySet()) {
                String field = entry.getKey();
                Object newValue = entry.getValue();
                Object oldValue = existingData.get(field);

                // 只记录有变更的字段
                if (!java.util.Objects.equals(
                        newValue == null ? null : newValue.toString(),
                        oldValue == null ? null : oldValue.toString())) {

                    SysDmlLog logEntry = new SysDmlLog();
                    logEntry.setLogId(java.util.UUID.randomUUID().toString().replace("-", ""));
                    logEntry.setTableId(getTableId());
                    logEntry.setRecordId(ZLPID);
                    logEntry.setColumnId(getTableId()+"__"+field);
                    logEntry.setAction("update");
                    logEntry.setActionTime(new java.util.Date());
                    logEntry.setOldValue(oldValue == null ? null : oldValue.toString());
                    logEntry.setNewValue(newValue == null ? null : newValue.toString());
                    logEntry.setActor(ShiroUtils.getSysUser().getUserId());
                    sysDmlLogService.doInsert(logEntry);
                }
            }
        }

        // 执行更新
        StringBuilder sql = new StringBuilder("UPDATE ZL_PLAN_POSITION SET ");
        List<Object> params = new ArrayList<>();
        boolean first = true;

        for (Map.Entry<String, Object> entry : updateData.entrySet()) {
            if (!first) sql.append(", ");
            sql.append(entry.getKey()).append(" = ?");
            params.add(entry.getValue());
            first = false;
        }

        sql.append(" WHERE ZLPID = ?");
        params.add(ZLPID);

        int updateCount = jdbcTemplate.update(sql.toString(), params.toArray());
        log.info("更新岗位数据: ZLPID={}, 影响行数={}, 更新字段={}", ZLPID, updateCount, updateData.keySet());

        return updateCount > 0;

    } catch (Exception e) {
        log.error("更新岗位数据失败: ZLPID={}", zlpId, e);
        throw e;
    }
}
