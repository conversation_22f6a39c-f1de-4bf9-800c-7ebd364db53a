<!DOCTYPE html>
<html>
<head>
    <title>日期时间选择器测试</title>
    <script type="text/javascript" src="https://cdn.webix.com/edge/webix.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.webix.com/edge/webix.css">
    <style>
        body { margin: 20px; font-family: Arial, sans-serif; }
        .test-panel { 
            background: #f0f8ff; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
            border-left: 4px solid #2196F3;
        }
        pre { 
            background: white; 
            padding: 10px; 
            border-radius: 3px; 
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>日期时间选择器测试</h1>
    
    <div class="test-panel">
        <h3>测试说明</h3>
        <p>这个页面测试不同类型的日期选择器：</p>
        <ul>
            <li><strong>daterangepicker</strong> - 只有日期，无时间</li>
            <li><strong>datetimerangepicker</strong> - 包含日期和时间</li>
        </ul>
    </div>

    <div id="testContainer"></div>
    <div id="resultContainer"></div>

    <script>
        // 测试配置
        const searchConfig = [
            {
                view: 'daterangepicker',
                searchLevel: 1,
                format: '%Y-%m-%d',
                timepicker: false,
                inputWidth: 300,
                label: '纯日期范围',
                labelWidth: 100,
                id: 'DATE_ONLY_FIELD',
                placeholder: '请选择日期范围'
            },
            {
                view: 'datetimerangepicker',
                searchLevel: 1,
                format: '%Y-%m-%d %H:%i',
                timepicker: true,
                inputWidth: 300,
                label: '日期时间范围',
                labelWidth: 100,
                id: 'DATETIME_FIELD',
                placeholder: '请选择日期时间范围'
            }
        ];

        const searchOptions = {
            columnsPerRow: 1,
            showButtons: true,
            onSearch: function(filter) {
                console.log("🔍 搜索回调:", filter);
                
                // 检查组件配置
                const dateOnlyStart = $$("default_DATE_ONLY_FIELD#1");
                const dateOnlyEnd = $$("default_DATE_ONLY_FIELD#2");
                const datetimeStart = $$("default_DATETIME_FIELD#1");
                const datetimeEnd = $$("default_DATETIME_FIELD#2");
                
                let resultHtml = `
                    <div class="test-panel">
                        <h3>组件配置检查</h3>
                        <h4>纯日期字段 (DATE_ONLY_FIELD):</h4>
                        <p><strong>开始日期组件:</strong> ${dateOnlyStart ? '✅ 存在' : '❌ 不存在'}</p>
                        ${dateOnlyStart ? `<p><strong>时间选择器:</strong> ${dateOnlyStart.config.timepicker ? '✅ 启用' : '❌ 禁用'}</p>` : ''}
                        ${dateOnlyStart ? `<p><strong>格式:</strong> ${dateOnlyStart.config.format}</p>` : ''}
                        
                        <h4>日期时间字段 (DATETIME_FIELD):</h4>
                        <p><strong>开始日期时间组件:</strong> ${datetimeStart ? '✅ 存在' : '❌ 不存在'}</p>
                        ${datetimeStart ? `<p><strong>视图类型:</strong> ${datetimeStart.config.view}</p>` : ''}
                        ${datetimeStart ? `<p><strong>时间选择器:</strong> ${datetimeStart.config.timepicker ? '✅ 启用' : '❌ 禁用'}</p>` : ''}
                        ${datetimeStart ? `<p><strong>格式:</strong> ${datetimeStart.config.format}</p>` : ''}
                        
                        <h4>搜索结果:</h4>
                        <pre>${JSON.stringify(filter, null, 2)}</pre>
                    </div>
                `;
                
                document.getElementById('resultContainer').innerHTML = resultHtml;
            }
        };

        // 页面加载完成后初始化
        webix.ready(function() {
            console.log("🚀 开始加载 WebixSearchUtil...");
            
            fetch('doc/2.txt')
                .then(response => response.text())
                .then(code => {
                    console.log("📁 WebixSearchUtil 代码加载完成");
                    eval(code);
                    
                    console.log("🔧 构建搜索UI...");
                    const searchUI = WebixSearchUtil.buildSearchUI(searchConfig, searchOptions);
                    
                    console.log("🔍 生成的搜索UI结构:", searchUI);
                    
                    webix.ui({
                        container: "testContainer",
                        rows: [
                            {
                                view: "toolbar",
                                elements: [
                                    { view: "label", label: "日期时间选择器测试" }
                                ]
                            },
                            {
                                view: "form",
                                elements: searchUI.defaultUI
                            }
                        ]
                    });
                    
                    console.log("✅ 界面创建完成");
                    
                    // 延迟检查组件配置
                    setTimeout(() => {
                        console.log("🔍 检查组件配置...");
                        
                        const dateOnlyStart = $$("default_DATE_ONLY_FIELD#1");
                        const datetimeStart = $$("default_DATETIME_FIELD#1");
                        
                        if (dateOnlyStart) {
                            console.log("📅 纯日期组件配置:", {
                                timepicker: dateOnlyStart.config.timepicker,
                                format: dateOnlyStart.config.format,
                                view: dateOnlyStart.config.view
                            });
                        }
                        
                        if (datetimeStart) {
                            console.log("📅 日期时间组件配置:", {
                                timepicker: datetimeStart.config.timepicker,
                                format: datetimeStart.config.format,
                                view: datetimeStart.config.view
                            });
                        }
                    }, 1000);
                })
                .catch(error => {
                    console.error("❌ 加载失败:", error);
                    document.getElementById('resultContainer').innerHTML = 
                        `<div class="test-panel"><h3>加载错误</h3><p>${error.message}</p></div>`;
                });
        });
    </script>
</body>
</html>
