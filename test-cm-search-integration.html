<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>测试 CM 类型搜索集成</title>
  <link rel="stylesheet" href="./webix/webix.css" type="text/css">
  <script src="./webix/webix.js"></script>
  <script src="./webix/i18n/zh.js"></script>
</head>
<body>
  <div id="test_container" style="margin: 20px;"></div>
  <div id="result_container" style="margin: 20px;"></div>

  <script>
    document.addEventListener("DOMContentLoaded", function() {
      webix.i18n.setLocale("zh-CN");

      // 模拟前端传递给后端的搜索参数
      const testSearchParams = {
        // 单选代码字段（C类型）
        "ZL_PLAN__ZLTYPE": "01",
        
        // 多选代码字段（CM类型）- 数组格式
        "ZL_PLAN__ZLSTATUS": ["01", "02", "03"],
        
        // 多选代码字段（CM类型）- 字符串格式
        "ZL_PLAN__ZLDEPT": "D001,D002,D003",
        
        // 普通字符串字段
        "ZL_PLAN__ZLTITLE": "测试标题",
        
        // 年份字段
        "ZL_PLAN__ZLYEAR": "2025"
      };

      // 模拟字段类型配置（来自数据库配置）
      const fieldTypeConfig = {
        "ZL_PLAN__ZLTYPE": "C",      // 单选代码
        "ZL_PLAN__ZLSTATUS": "CM",   // 多选代码
        "ZL_PLAN__ZLDEPT": "CM",     // 多选代码
        "ZL_PLAN__ZLTITLE": "S",     // 字符串
        "ZL_PLAN__ZLYEAR": "S"       // 字符串
      };

      // 模拟Java后端的buildFieldCondition逻辑
      function simulateBuildFieldCondition(fieldName, fieldType, value) {
        console.log(`🔍 [模拟] 处理字段: ${fieldName}, 类型: ${fieldType}, 值:`, value);
        
        if (value == null || value === "") {
          return null;
        }

        switch (fieldType) {
          case "S":
          case "M":
            // 字符串类型 - LIKE 查询
            return {
              type: "STRING_LIKE",
              field: fieldName,
              condition: `${fieldName} LIKE '%${value}%'`
            };
            
          case "C":
            // 单选代码类型 - 等值查询
            if (value !== "-99") { // -99是全部选项
              return {
                type: "CODE_EQUAL",
                field: fieldName,
                condition: `${fieldName} = '${value}'`
              };
            }
            break;
            
          case "CM":
            // 多选代码类型 - IN 查询
            console.log(`🔍 [CM] 处理多选代码字段: ${fieldName}`);
            console.log(`🔍 [CM] 原始值:`, value, `类型: ${typeof value}`);
            
            let validValues = [];
            
            if (Array.isArray(value)) {
              // 数组类型
              console.log(`🔍 [CM] 处理数组类型，长度: ${value.length}`);
              validValues = value.filter(v => v != null && v !== "" && v !== "-99");
            } else if (typeof value === "string") {
              // 字符串类型（逗号分隔）
              console.log(`🔍 [CM] 处理字符串类型: ${value}`);
              validValues = value.split(",")
                .map(v => v.trim())
                .filter(v => v !== "" && v !== "-99");
            } else {
              // 其他类型当作单个值
              console.log(`🔍 [CM] 处理其他类型: ${value}`);
              if (value !== "-99") {
                validValues = [value.toString()];
              }
            }
            
            if (validValues.length > 0) {
              console.log(`🔍 [CM] 有效值列表:`, validValues);
              const inClause = validValues.map(v => `'${v}'`).join(", ");
              return {
                type: "CODE_MULTI_IN",
                field: fieldName,
                condition: `${fieldName} IN (${inClause})`,
                values: validValues
              };
            }
            break;
            
          default:
            // 默认字符串处理
            return {
              type: "DEFAULT_LIKE",
              field: fieldName,
              condition: `${fieldName} LIKE '%${value}%'`
            };
        }
        
        return null;
      }

      // 模拟构建完整的WHERE条件
      function buildWhereConditions(searchParams, fieldTypes) {
        console.log("🔍 [模拟] 开始构建WHERE条件");
        console.log("🔍 [模拟] 搜索参数:", searchParams);
        console.log("🔍 [模拟] 字段类型:", fieldTypes);
        
        const conditions = [];
        
        for (const [fieldName, value] of Object.entries(searchParams)) {
          const fieldType = fieldTypes[fieldName] || "S";
          const condition = simulateBuildFieldCondition(fieldName, fieldType, value);
          
          if (condition) {
            conditions.push(condition);
            console.log(`✅ [模拟] 字段 ${fieldName} 条件构建成功:`, condition);
          } else {
            console.log(`⚠️ [模拟] 字段 ${fieldName} 跳过（值为空或无效）`);
          }
        }
        
        return conditions;
      }

      // 执行测试
      console.log("🚀 开始测试CM类型搜索集成");
      const whereConditions = buildWhereConditions(testSearchParams, fieldTypeConfig);
      
      console.log("🎉 测试完成，生成的WHERE条件:");
      console.log(whereConditions);

      // 显示结果
      const resultHtml = `
        <h3>CM类型搜索集成测试结果</h3>
        <h4>输入参数：</h4>
        <pre>${JSON.stringify(testSearchParams, null, 2)}</pre>
        
        <h4>字段类型配置：</h4>
        <pre>${JSON.stringify(fieldTypeConfig, null, 2)}</pre>
        
        <h4>生成的SQL条件：</h4>
        <div style="background: #f5f5f5; padding: 10px; border-radius: 4px;">
          ${whereConditions.map(cond => 
            `<div style="margin: 5px 0;">
              <strong>${cond.type}:</strong> ${cond.condition}
              ${cond.values ? `<br><small>值: [${cond.values.join(', ')}]</small>` : ''}
            </div>`
          ).join('')}
        </div>
        
        <h4>关键检查点：</h4>
        <ul>
          <li>✅ CM类型字段是否使用IN查询？${whereConditions.some(c => c.type === 'CODE_MULTI_IN') ? '是' : '否'}</li>
          <li>✅ 数组值是否正确处理？${whereConditions.find(c => c.field === 'ZL_PLAN__ZLSTATUS')?.values?.length === 3 ? '是' : '否'}</li>
          <li>✅ 字符串值是否正确分割？${whereConditions.find(c => c.field === 'ZL_PLAN__ZLDEPT')?.values?.length === 3 ? '是' : '否'}</li>
          <li>✅ C类型是否使用等值查询？${whereConditions.some(c => c.type === 'CODE_EQUAL') ? '是' : '否'}</li>
        </ul>
        
        <h4>预期的Java QueryDSL代码：</h4>
        <pre style="background: #f0f0f0; padding: 10px; border-radius: 4px;">
// 单选代码字段
where = where.and(table.getString("ZLTYPE").eq("01"));

// 多选代码字段（数组）
where = where.and(table.getString("ZLSTATUS").in(Arrays.asList("01", "02", "03")));

// 多选代码字段（字符串）
where = where.and(table.getString("ZLDEPT").in(Arrays.asList("D001", "D002", "D003")));

// 字符串字段
where = where.and(table.getString("ZLTITLE").like("%测试标题%"));
        </pre>
      `;

      webix.ui({
        container: "test_container",
        view: "label",
        label: "CM类型搜索集成测试 - 请查看浏览器控制台获取详细日志",
        css: "webix_header"
      });

      webix.ui({
        container: "result_container",
        view: "template",
        template: resultHtml,
        height: 600,
        scroll: "y"
      });
    });
  </script>
</body>
</html>
